#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏幕截图模块
负责捕获屏幕内容并进行预处理
"""

import cv2
import numpy as np
import logging
from typing import Optional, Tuple, Dict, Any
from PIL import Image, ImageGrab
import mss
import time

logger = logging.getLogger(__name__)


class ScreenCapture:
    """屏幕捕获类"""
    
    def __init__(self):
        self.sct = None
        self.monitor = None
        self.capture_region = None
        self.last_screenshot = None
        self.last_capture_time = 0
        
    def initialize(self) -> bool:
        """初始化屏幕捕获"""
        try:
            # 初始化mss
            self.sct = mss.mss()
            
            # 获取主显示器信息
            self.monitor = self.sct.monitors[1]  # monitors[0]是所有显示器的组合
            
            logger.info(f"屏幕分辨率: {self.monitor['width']}x{self.monitor['height']}")
            
            # 设置默认捕获区域为全屏
            self.capture_region = {
                'top': 0,
                'left': 0,
                'width': self.monitor['width'],
                'height': self.monitor['height']
            }
            
            return True
            
        except Exception as e:
            logger.error(f"屏幕捕获初始化失败: {e}")
            return False
    
    def set_capture_region(self, x: int, y: int, width: int, height: int):
        """设置捕获区域"""
        self.capture_region = {
            'top': y,
            'left': x,
            'width': width,
            'height': height
        }
        logger.info(f"设置捕获区域: ({x}, {y}) {width}x{height}")
    
    def capture(self) -> Optional[np.ndarray]:
        """捕获屏幕"""
        try:
            current_time = time.time()
            
            # 限制捕获频率，避免过于频繁
            if current_time - self.last_capture_time < 0.1:  # 最多每100ms捕获一次
                return self.last_screenshot
            
            # 使用mss捕获屏幕
            screenshot = self.sct.grab(self.capture_region)
            
            # 转换为numpy数组
            img_array = np.array(screenshot)
            
            # 转换颜色格式 (BGRA -> BGR)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
            
            self.last_screenshot = img_bgr
            self.last_capture_time = current_time
            
            return img_bgr
            
        except Exception as e:
            logger.error(f"屏幕捕获失败: {e}")
            return None
    
    def capture_pil(self) -> Optional[Image.Image]:
        """使用PIL捕获屏幕"""
        try:
            if self.capture_region:
                bbox = (
                    self.capture_region['left'],
                    self.capture_region['top'],
                    self.capture_region['left'] + self.capture_region['width'],
                    self.capture_region['top'] + self.capture_region['height']
                )
                screenshot = ImageGrab.grab(bbox)
            else:
                screenshot = ImageGrab.grab()
            
            return screenshot
            
        except Exception as e:
            logger.error(f"PIL屏幕捕获失败: {e}")
            return None
    
    def save_screenshot(self, filename: str, img: Optional[np.ndarray] = None) -> bool:
        """保存截图"""
        try:
            if img is None:
                img = self.capture()
            
            if img is None:
                return False
            
            cv2.imwrite(filename, img)
            logger.info(f"截图已保存: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存截图失败: {e}")
            return False
    
    def preprocess_image(self, img: np.ndarray, operations: list = None) -> np.ndarray:
        """预处理图像"""
        if operations is None:
            operations = ['resize', 'denoise']
        
        processed_img = img.copy()
        
        try:
            for operation in operations:
                if operation == 'resize':
                    # 调整大小以提高处理速度
                    height, width = processed_img.shape[:2]
                    if width > 1920:
                        scale = 1920 / width
                        new_width = int(width * scale)
                        new_height = int(height * scale)
                        processed_img = cv2.resize(processed_img, (new_width, new_height))
                
                elif operation == 'denoise':
                    # 降噪
                    processed_img = cv2.fastNlMeansDenoisingColored(processed_img)
                
                elif operation == 'sharpen':
                    # 锐化
                    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                    processed_img = cv2.filter2D(processed_img, -1, kernel)
                
                elif operation == 'contrast':
                    # 增强对比度
                    processed_img = cv2.convertScaleAbs(processed_img, alpha=1.2, beta=10)
            
            return processed_img
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return img
    
    def find_template(self, template_path: str, threshold: float = 0.8) -> Optional[Dict[str, Any]]:
        """在当前屏幕中查找模板图像"""
        try:
            # 捕获当前屏幕
            screenshot = self.capture()
            if screenshot is None:
                return None
            
            # 读取模板图像
            template = cv2.imread(template_path)
            if template is None:
                logger.error(f"无法读取模板图像: {template_path}")
                return None
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # 计算匹配区域的中心点
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                return {
                    'found': True,
                    'confidence': max_val,
                    'position': (center_x, center_y),
                    'top_left': max_loc,
                    'bottom_right': (max_loc[0] + w, max_loc[1] + h)
                }
            else:
                return {'found': False, 'confidence': max_val}
                
        except Exception as e:
            logger.error(f"模板匹配失败: {e}")
            return None
    
    def find_color_region(self, color_range: Dict[str, Tuple], min_area: int = 100) -> list:
        """查找指定颜色区域"""
        try:
            screenshot = self.capture()
            if screenshot is None:
                return []
            
            # 转换到HSV颜色空间
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            # 创建颜色掩码
            lower = np.array(color_range['lower'])
            upper = np.array(color_range['upper'])
            mask = cv2.inRange(hsv, lower, upper)
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            regions = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= min_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    center_x = x + w // 2
                    center_y = y + h // 2
                    
                    regions.append({
                        'center': (center_x, center_y),
                        'bbox': (x, y, w, h),
                        'area': area
                    })
            
            return regions
            
        except Exception as e:
            logger.error(f"颜色区域查找失败: {e}")
            return []
    
    def get_pixel_color(self, x: int, y: int) -> Optional[Tuple[int, int, int]]:
        """获取指定位置的像素颜色"""
        try:
            screenshot = self.capture()
            if screenshot is None:
                return None
            
            if 0 <= y < screenshot.shape[0] and 0 <= x < screenshot.shape[1]:
                # OpenCV使用BGR格式
                b, g, r = screenshot[y, x]
                return (int(r), int(g), int(b))  # 返回RGB格式
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取像素颜色失败: {e}")
            return None
    
    def cleanup(self):
        """清理资源"""
        if self.sct:
            self.sct.close()
        logger.info("屏幕捕获资源已清理")

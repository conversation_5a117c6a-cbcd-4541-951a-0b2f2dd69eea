{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Data URLs are not a valid image source.": "資料 URL 不是有效的影像來源。", "Embedded SVGs are not a valid image source.": "內嵌 SVGs 不是有效的影像來源。", "Error parsing the when-clause:": "剖析 when 子句時發生錯誤:", "Images must use the HTTPS protocol.": "影像必須使用 HTTPS 通訊協定。", "Language specific editor settings": "語言專用編輯器設定", "Override editor settings for language": "針對語言覆寫編輯器設定", "Relative badge URLs require a repository with HTTPS protocol to be specified in this package.json.": "相對徽章 URL 必須在 package.json 中指定使用 HTTPS 通訊協定的存放庫。", "Relative image URLs require a repository with HTTPS protocol to be specified in the package.json.": "相對影像 URL 必須在 package.json 中指定使用 HTTPS 通訊協定的存放庫。", "Remove activation event": "移除啟用事件", "SVGs are not a valid image source.": "SVGs 不是有效的影像來源。", "This activation event can be removed as VS Code generates these automatically from your package.json contribution declarations.": "此啟用事件可移除，因為 VS Code 會從您的 package.json 貢獻宣告自動產生這些事件。", "This activation event can be removed for extensions targeting engine version ^1.75 as VS Code will generate these automatically from your package.json contribution declarations.": "此啟用事件可針對以引擎版本 ^1.75 為目標的擴延伸模組移除，因為 VS Code 會從您的 package.json 貢獻宣告自動產生這些項目。", "This activation event cannot be explicitly listed by your extension.": "您的延伸模組無法明確列出此啟用事件。", "This proposal cannot be used because for this extension the product defines a fixed set of API proposals. You can test your extension but before publishing you MUST reach out to the VS Code team.": "此專案無法使用，因為產品為此延伸模組定義了一組固定的 API 專案。您可以測試延伸模組，但在發布之前，必須先連絡 VS Code 小組。", "Using '*' activation is usually a bad idea as it impacts performance.": "使用 '*' 啟用通常是個錯誤的想法，因為它會影響效能。"}, "package": {"description": "提供 lint 功能以撰寫延伸模組。", "displayName": "延伸模組撰寫"}}}
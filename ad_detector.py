#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广告检测模块
负责识别屏幕中的广告内容和相关按钮
"""

import cv2
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
import os
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class AdDetector:
    """广告检测器"""
    
    def __init__(self):
        self.templates_dir = "templates"
        self.ad_templates = {}
        self.button_templates = {}
        self.color_patterns = {}
        self.text_patterns = []
        self.confidence_threshold = 0.7
        
    def initialize(self) -> bool:
        """初始化广告检测器"""
        try:
            # 创建模板目录
            os.makedirs(self.templates_dir, exist_ok=True)
            
            # 加载广告模板
            self._load_templates()
            
            # 初始化颜色模式
            self._init_color_patterns()
            
            # 初始化文本模式
            self._init_text_patterns()
            
            logger.info("广告检测器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"广告检测器初始化失败: {e}")
            return False
    
    def _load_templates(self):
        """加载广告模板图像"""
        try:
            # 广告相关模板
            ad_template_files = {
                'close_button': 'close_button.png',
                'skip_button': 'skip_button.png',
                'video_ad_indicator': 'video_ad.png',
                'banner_ad': 'banner_ad.png',
                'popup_ad': 'popup_ad.png',
                'reward_button': 'reward_button.png',
                'watch_ad_button': 'watch_ad.png'
            }
            
            for template_name, filename in ad_template_files.items():
                template_path = os.path.join(self.templates_dir, filename)
                if os.path.exists(template_path):
                    template = cv2.imread(template_path)
                    if template is not None:
                        self.ad_templates[template_name] = template
                        logger.debug(f"加载模板: {template_name}")
            
            # 如果没有模板文件，创建默认模板
            if not self.ad_templates:
                self._create_default_templates()
                
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
    
    def _create_default_templates(self):
        """创建默认的检测模板"""
        # 这里可以创建一些基本的几何形状作为默认模板
        # 实际使用时应该替换为真实的广告截图
        
        # 创建关闭按钮模板 (X形状)
        close_template = np.zeros((30, 30, 3), dtype=np.uint8)
        cv2.line(close_template, (5, 5), (25, 25), (255, 255, 255), 2)
        cv2.line(close_template, (25, 5), (5, 25), (255, 255, 255), 2)
        self.ad_templates['close_button'] = close_template
        
        # 保存默认模板
        cv2.imwrite(os.path.join(self.templates_dir, 'close_button.png'), close_template)
        
        logger.info("已创建默认检测模板")
    
    def _init_color_patterns(self):
        """初始化颜色模式"""
        # 常见广告颜色模式 (HSV格式)
        self.color_patterns = {
            'red_button': {
                'lower': (0, 120, 120),
                'upper': (10, 255, 255)
            },
            'green_button': {
                'lower': (40, 120, 120),
                'upper': (80, 255, 255)
            },
            'blue_button': {
                'lower': (100, 120, 120),
                'upper': (130, 255, 255)
            },
            'yellow_button': {
                'lower': (20, 120, 120),
                'upper': (30, 255, 255)
            },
            'white_text': {
                'lower': (0, 0, 200),
                'upper': (180, 30, 255)
            }
        }
    
    def _init_text_patterns(self):
        """初始化文本模式"""
        # 常见的广告相关文本
        self.text_patterns = [
            "跳过广告", "Skip Ad", "关闭", "Close", "×",
            "观看广告", "Watch Ad", "获得奖励", "Get Reward",
            "免费获得", "Free", "立即观看", "Watch Now",
            "继续", "Continue", "下一个", "Next"
        ]
    
    def detect_ad(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """检测广告"""
        try:
            ad_info = {
                'has_ad': False,
                'type': None,
                'confidence': 0.0,
                'position': None,
                'duration': 30,  # 默认广告时长
                'reward': 10     # 默认奖励
            }
            
            # 检测视频广告
            video_ad = self._detect_video_ad(screenshot)
            if video_ad['found']:
                ad_info.update({
                    'has_ad': True,
                    'type': 'video_ad',
                    'confidence': video_ad['confidence'],
                    'position': video_ad['position'],
                    'duration': video_ad.get('duration', 30)
                })
                return ad_info
            
            # 检测横幅广告
            banner_ad = self._detect_banner_ad(screenshot)
            if banner_ad['found']:
                ad_info.update({
                    'has_ad': True,
                    'type': 'banner_ad',
                    'confidence': banner_ad['confidence'],
                    'position': banner_ad['position']
                })
                return ad_info
            
            # 检测弹窗广告
            popup_ad = self._detect_popup_ad(screenshot)
            if popup_ad['found']:
                ad_info.update({
                    'has_ad': True,
                    'type': 'popup_ad',
                    'confidence': popup_ad['confidence'],
                    'position': popup_ad['position']
                })
                return ad_info
            
            return ad_info
            
        except Exception as e:
            logger.error(f"广告检测失败: {e}")
            return {'has_ad': False, 'type': None, 'confidence': 0.0}
    
    def _detect_video_ad(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """检测视频广告"""
        result = {'found': False, 'confidence': 0.0}
        
        try:
            # 方法1: 模板匹配
            if 'video_ad_indicator' in self.ad_templates:
                template_result = self._match_template(
                    screenshot, 
                    self.ad_templates['video_ad_indicator']
                )
                if template_result['found']:
                    result.update(template_result)
                    return result
            
            # 方法2: 检测视频播放器特征
            player_features = self._detect_video_player_features(screenshot)
            if player_features['found']:
                result.update(player_features)
                return result
            
            # 方法3: 检测广告倒计时
            countdown = self._detect_ad_countdown(screenshot)
            if countdown['found']:
                result.update(countdown)
                result['duration'] = countdown.get('remaining_time', 30)
                return result
            
        except Exception as e:
            logger.error(f"视频广告检测失败: {e}")
        
        return result
    
    def _detect_banner_ad(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """检测横幅广告"""
        result = {'found': False, 'confidence': 0.0}
        
        try:
            # 检测横幅广告的特征：通常在屏幕顶部或底部
            height, width = screenshot.shape[:2]
            
            # 检查顶部区域
            top_region = screenshot[0:height//4, :]
            top_result = self._analyze_ad_region(top_region)
            if top_result['is_ad']:
                result = {
                    'found': True,
                    'confidence': top_result['confidence'],
                    'position': (width//2, height//8),
                    'region': 'top'
                }
                return result
            
            # 检查底部区域
            bottom_region = screenshot[3*height//4:, :]
            bottom_result = self._analyze_ad_region(bottom_region)
            if bottom_result['is_ad']:
                result = {
                    'found': True,
                    'confidence': bottom_result['confidence'],
                    'position': (width//2, 7*height//8),
                    'region': 'bottom'
                }
                return result
            
        except Exception as e:
            logger.error(f"横幅广告检测失败: {e}")
        
        return result
    
    def _detect_popup_ad(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """检测弹窗广告"""
        result = {'found': False, 'confidence': 0.0}
        
        try:
            # 检测弹窗特征：通常在屏幕中央，有边框
            height, width = screenshot.shape[:2]
            
            # 检查中央区域
            center_region = screenshot[
                height//4:3*height//4, 
                width//4:3*width//4
            ]
            
            popup_result = self._analyze_popup_region(center_region)
            if popup_result['is_popup']:
                result = {
                    'found': True,
                    'confidence': popup_result['confidence'],
                    'position': (width//2, height//2)
                }
            
        except Exception as e:
            logger.error(f"弹窗广告检测失败: {e}")
        
        return result
    
    def _detect_video_player_features(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """检测视频播放器特征"""
        # 检测播放按钮、进度条等视频播放器元素
        # 这里简化实现，实际可以使用更复杂的特征检测
        
        gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        
        # 检测圆形播放按钮
        circles = cv2.HoughCircles(
            gray, cv2.HOUGH_GRADIENT, 1, 50,
            param1=50, param2=30, minRadius=20, maxRadius=100
        )
        
        if circles is not None:
            return {
                'found': True,
                'confidence': 0.8,
                'position': (int(circles[0][0][0]), int(circles[0][0][1]))
            }
        
        return {'found': False}
    
    def _detect_ad_countdown(self, screenshot: np.ndarray) -> Dict[str, Any]:
        """检测广告倒计时"""
        # 这里可以使用OCR来识别倒计时数字
        # 简化实现：检测特定位置的数字模式
        
        # 通常倒计时在右上角
        height, width = screenshot.shape[:2]
        countdown_region = screenshot[0:height//6, 2*width//3:]
        
        # 检测数字特征（这里简化为检测白色文本）
        gray = cv2.cvtColor(countdown_region, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
        
        # 计算白色像素比例
        white_ratio = np.sum(thresh == 255) / thresh.size
        
        if white_ratio > 0.1:  # 如果有足够的白色像素，可能是倒计时
            return {
                'found': True,
                'confidence': 0.6,
                'position': (5*width//6, height//12),
                'remaining_time': 15  # 估计剩余时间
            }
        
        return {'found': False}
    
    def _analyze_ad_region(self, region: np.ndarray) -> Dict[str, Any]:
        """分析区域是否为广告"""
        # 简化的广告检测逻辑
        # 实际应用中可以使用机器学习模型
        
        # 检测颜色丰富度
        hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([hsv], [0, 1], None, [50, 60], [0, 180, 0, 256])
        color_diversity = np.count_nonzero(hist) / hist.size
        
        # 检测边缘密度
        gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # 综合评分
        ad_score = (color_diversity * 0.6 + edge_density * 0.4)
        
        return {
            'is_ad': ad_score > 0.3,
            'confidence': min(ad_score, 1.0)
        }
    
    def _analyze_popup_region(self, region: np.ndarray) -> Dict[str, Any]:
        """分析区域是否为弹窗"""
        # 检测矩形边框
        gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # 检测矩形轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            # 检测矩形度
            area = cv2.contourArea(contour)
            if area > 1000:  # 足够大的区域
                x, y, w, h = cv2.boundingRect(contour)
                rect_area = w * h
                rectangularity = area / rect_area
                
                if rectangularity > 0.8:  # 高矩形度
                    return {
                        'is_popup': True,
                        'confidence': rectangularity
                    }
        
        return {'is_popup': False, 'confidence': 0.0}
    
    def _match_template(self, screenshot: np.ndarray, template: np.ndarray) -> Dict[str, Any]:
        """模板匹配"""
        try:
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= self.confidence_threshold:
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                return {
                    'found': True,
                    'confidence': max_val,
                    'position': (center_x, center_y)
                }
            
        except Exception as e:
            logger.error(f"模板匹配失败: {e}")
        
        return {'found': False, 'confidence': 0.0}
    
    def find_close_button(self, screenshot: np.ndarray) -> Optional[Dict[str, Any]]:
        """查找关闭按钮"""
        try:
            # 方法1: 模板匹配
            if 'close_button' in self.ad_templates:
                result = self._match_template(screenshot, self.ad_templates['close_button'])
                if result['found']:
                    return {
                        'x': result['position'][0],
                        'y': result['position'][1],
                        'confidence': result['confidence']
                    }
            
            # 方法2: 颜色检测 (查找白色X或关闭图标)
            close_buttons = self._find_close_button_by_color(screenshot)
            if close_buttons:
                return close_buttons[0]  # 返回第一个找到的
            
            # 方法3: 在常见位置查找
            common_positions = self._check_common_close_positions(screenshot)
            if common_positions:
                return common_positions[0]
            
        except Exception as e:
            logger.error(f"查找关闭按钮失败: {e}")
        
        return None
    
    def _find_close_button_by_color(self, screenshot: np.ndarray) -> List[Dict[str, Any]]:
        """通过颜色查找关闭按钮"""
        buttons = []
        
        # 查找白色区域（可能是关闭按钮）
        if 'white_text' in self.color_patterns:
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            pattern = self.color_patterns['white_text']
            mask = cv2.inRange(hsv, np.array(pattern['lower']), np.array(pattern['upper']))
            
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 100 < area < 2000:  # 合适的按钮大小
                    x, y, w, h = cv2.boundingRect(contour)
                    # 检查长宽比，关闭按钮通常接近正方形
                    aspect_ratio = w / h
                    if 0.5 < aspect_ratio < 2.0:
                        buttons.append({
                            'x': x + w // 2,
                            'y': y + h // 2,
                            'confidence': 0.7
                        })
        
        return buttons
    
    def _check_common_close_positions(self, screenshot: np.ndarray) -> List[Dict[str, Any]]:
        """检查常见的关闭按钮位置"""
        height, width = screenshot.shape[:2]
        positions = []
        
        # 常见位置：右上角
        common_areas = [
            (width - 50, 50),      # 右上角
            (width - 100, 50),     # 右上角偏左
            (width - 50, 100),     # 右上角偏下
        ]
        
        for x, y in common_areas:
            if self._is_likely_close_button(screenshot, x, y):
                positions.append({
                    'x': x,
                    'y': y,
                    'confidence': 0.5
                })
        
        return positions
    
    def _is_likely_close_button(self, screenshot: np.ndarray, x: int, y: int, size: int = 30) -> bool:
        """检查指定位置是否可能是关闭按钮"""
        try:
            height, width = screenshot.shape[:2]
            
            # 确保坐标在有效范围内
            x1 = max(0, x - size // 2)
            y1 = max(0, y - size // 2)
            x2 = min(width, x + size // 2)
            y2 = min(height, y + size // 2)
            
            region = screenshot[y1:y2, x1:x2]
            
            if region.size == 0:
                return False
            
            # 检测是否有对比度较高的图案（如X形状）
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edge_ratio = np.sum(edges > 0) / edges.size
            
            return edge_ratio > 0.1
            
        except Exception as e:
            logger.error(f"检查关闭按钮失败: {e}")
            return False
    
    def find_task_buttons(self, screenshot: np.ndarray) -> List[Dict[str, Any]]:
        """查找任务相关按钮"""
        buttons = []
        
        try:
            # 查找"观看广告"按钮
            if 'watch_ad_button' in self.ad_templates:
                result = self._match_template(screenshot, self.ad_templates['watch_ad_button'])
                if result['found']:
                    buttons.append({
                        'type': 'watch_ad_button',
                        'x': result['position'][0],
                        'y': result['position'][1],
                        'confidence': result['confidence']
                    })
            
            # 查找奖励按钮
            if 'reward_button' in self.ad_templates:
                result = self._match_template(screenshot, self.ad_templates['reward_button'])
                if result['found']:
                    buttons.append({
                        'type': 'reward_button',
                        'x': result['position'][0],
                        'y': result['position'][1],
                        'confidence': result['confidence']
                    })
            
            # 通过颜色查找按钮
            color_buttons = self._find_buttons_by_color(screenshot)
            buttons.extend(color_buttons)
            
        except Exception as e:
            logger.error(f"查找任务按钮失败: {e}")
        
        return buttons
    
    def _find_buttons_by_color(self, screenshot: np.ndarray) -> List[Dict[str, Any]]:
        """通过颜色查找按钮"""
        buttons = []
        
        # 查找常见的按钮颜色
        button_colors = ['red_button', 'green_button', 'blue_button']
        
        for color_name in button_colors:
            if color_name in self.color_patterns:
                hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
                pattern = self.color_patterns[color_name]
                mask = cv2.inRange(hsv, np.array(pattern['lower']), np.array(pattern['upper']))
                
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 500 < area < 10000:  # 按钮大小范围
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h
                        if 0.3 < aspect_ratio < 3.0:  # 合理的长宽比
                            buttons.append({
                                'type': f'{color_name}_task',
                                'x': x + w // 2,
                                'y': y + h // 2,
                                'confidence': 0.6
                            })
        
        return buttons

    def add_template(self, name: str, image_path: str) -> bool:
        """添加新的检测模板"""
        try:
            template = cv2.imread(image_path)
            if template is not None:
                self.ad_templates[name] = template
                logger.info(f"添加模板: {name}")
                return True
            else:
                logger.error(f"无法读取模板图像: {image_path}")
                return False
        except Exception as e:
            logger.error(f"添加模板失败: {e}")
            return False

    def save_detection_result(self, screenshot: np.ndarray, result: Dict[str, Any], filename: str = None):
        """保存检测结果（用于调试）"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"detection_result_{timestamp}.jpg"

            # 在截图上标记检测结果
            result_img = screenshot.copy()

            if result.get('has_ad') and result.get('position'):
                x, y = result['position']
                cv2.circle(result_img, (x, y), 20, (0, 255, 0), 2)
                cv2.putText(result_img, result['type'], (x-50, y-30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            cv2.imwrite(filename, result_img)
            logger.info(f"检测结果已保存: {filename}")

        except Exception as e:
            logger.error(f"保存检测结果失败: {e}")

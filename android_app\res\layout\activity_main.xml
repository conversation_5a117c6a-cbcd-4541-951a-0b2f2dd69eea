<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5"
    tools:context=".MainActivity">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/header_background"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_bluetooth"
            android:tint="#ffffff" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="蓝牙自动化控制"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#ffffff"
            android:layout_marginStart="12dp" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_settings"
            android:tint="#ffffff"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackgroundBorderless" />

    </LinearLayout>

    <!-- 连接状态卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设备状态"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设备未连接"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="12dp" />

            <Button
                android:id="@+id/btn_connect"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="连接设备"
                android:textColor="#ffffff"
                android:background="@drawable/button_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 控制按钮卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="自动化控制"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <Button
                    android:id="@+id/btn_start_task"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始任务"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_success"
                    android:layout_marginEnd="8dp"
                    android:enabled="false" />

                <Button
                    android:id="@+id/btn_stop_task"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="停止任务"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_danger"
                    android:layout_marginStart="8dp"
                    android:enabled="false" />

            </LinearLayout>

            <Button
                android:id="@+id/btn_screenshot"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="测试截图"
                android:textColor="#333333"
                android:background="@drawable/button_secondary"
                android:enabled="false" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 统计信息卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="运行统计"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tv_stats"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已观看广告: 0\n获得金币: 0"
                android:textSize="14sp"
                android:textColor="#666666"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 日志显示区域 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="运行日志"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333" />

                <ImageView
                    android:id="@+id/btn_clear_log"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_clear"
                    android:tint="#666666"
                    android:clickable="true"
                    android:background="?android:attr/selectableItemBackgroundBorderless" />

            </LinearLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#f9f9f9"
                android:padding="8dp">

                <TextView
                    android:id="@+id/tv_log"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="等待连接设备..."
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:fontFamily="monospace"
                    android:lineSpacingExtra="2dp" />

            </ScrollView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/btn_settings"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_settings"
            android:tint="#666666"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="12dp"
            android:layout_marginEnd="16dp" />

        <ImageView
            android:id="@+id/btn_help"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_help"
            android:tint="#666666"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="12dp"
            android:layout_marginEnd="16dp" />

        <ImageView
            android:id="@+id/btn_info"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_info"
            android:tint="#666666"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="12dp" />

    </LinearLayout>

</LinearLayout>

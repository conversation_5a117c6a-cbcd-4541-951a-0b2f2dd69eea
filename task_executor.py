#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务执行模块
负责执行具体的自动化操作，如点击、滑动等
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
import pyautogui
import random

logger = logging.getLogger(__name__)


class TaskExecutor:
    """任务执行器"""
    
    def __init__(self):
        # 配置pyautogui
        pyautogui.FAILSAFE = True  # 鼠标移到左上角时停止
        pyautogui.PAUSE = 0.1      # 操作间隔
        
        self.last_action_time = 0
        self.min_action_interval = 0.5  # 最小操作间隔
        
    async def click_position(self, x: int, y: int, button: str = 'left', clicks: int = 1) -> bool:
        """点击指定位置"""
        try:
            # 确保操作间隔
            await self._ensure_action_interval()
            
            # 添加随机偏移，模拟人类操作
            offset_x = random.randint(-3, 3)
            offset_y = random.randint(-3, 3)
            
            final_x = x + offset_x
            final_y = y + offset_y
            
            logger.info(f"点击位置: ({final_x}, {final_y})")
            
            # 先移动鼠标到目标位置
            await self.move_mouse(final_x, final_y)
            
            # 执行点击
            pyautogui.click(final_x, final_y, button=button, clicks=clicks)
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"点击操作失败: {e}")
            return False
    
    async def move_mouse(self, x: int, y: int, duration: float = 0.2) -> bool:
        """移动鼠标到指定位置"""
        try:
            # 获取当前鼠标位置
            current_x, current_y = pyautogui.position()
            
            # 计算移动距离
            distance = ((x - current_x) ** 2 + (y - current_y) ** 2) ** 0.5
            
            # 根据距离调整移动时间
            if distance > 200:
                duration = min(duration * 2, 1.0)
            
            # 移动鼠标
            pyautogui.moveTo(x, y, duration=duration)
            
            return True
            
        except Exception as e:
            logger.error(f"鼠标移动失败: {e}")
            return False
    
    async def drag_mouse(self, start_x: int, start_y: int, end_x: int, end_y: int, 
                        duration: float = 0.5) -> bool:
        """拖拽鼠标"""
        try:
            await self._ensure_action_interval()
            
            logger.info(f"拖拽: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
            
            # 移动到起始位置
            await self.move_mouse(start_x, start_y)
            
            # 执行拖拽
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"拖拽操作失败: {e}")
            return False
    
    async def scroll(self, x: int, y: int, clicks: int = 3, direction: str = 'up') -> bool:
        """滚动操作"""
        try:
            await self._ensure_action_interval()
            
            # 移动到指定位置
            await self.move_mouse(x, y)
            
            # 执行滚动
            scroll_amount = clicks if direction == 'up' else -clicks
            pyautogui.scroll(scroll_amount, x=x, y=y)
            
            logger.info(f"滚动: ({x}, {y}) {direction} {clicks}次")
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"滚动操作失败: {e}")
            return False
    
    async def type_text(self, text: str, interval: float = 0.05) -> bool:
        """输入文本"""
        try:
            await self._ensure_action_interval()
            
            logger.info(f"输入文本: {text}")
            
            # 逐字符输入，模拟人类打字
            for char in text:
                pyautogui.write(char)
                await asyncio.sleep(interval + random.uniform(-0.02, 0.02))
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"文本输入失败: {e}")
            return False
    
    async def press_key(self, key: str, presses: int = 1) -> bool:
        """按键操作"""
        try:
            await self._ensure_action_interval()
            
            logger.info(f"按键: {key} x{presses}")
            
            for _ in range(presses):
                pyautogui.press(key)
                if presses > 1:
                    await asyncio.sleep(0.1)
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"按键操作失败: {e}")
            return False
    
    async def key_combination(self, keys: List[str]) -> bool:
        """组合键操作"""
        try:
            await self._ensure_action_interval()
            
            logger.info(f"组合键: {'+'.join(keys)}")
            
            pyautogui.hotkey(*keys)
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"组合键操作失败: {e}")
            return False
    
    async def wait_and_click(self, x: int, y: int, wait_time: float = 1.0) -> bool:
        """等待后点击"""
        await asyncio.sleep(wait_time)
        return await self.click_position(x, y)
    
    async def double_click(self, x: int, y: int) -> bool:
        """双击"""
        return await self.click_position(x, y, clicks=2)
    
    async def right_click(self, x: int, y: int) -> bool:
        """右键点击"""
        return await self.click_position(x, y, button='right')
    
    async def swipe_gesture(self, start_x: int, start_y: int, end_x: int, end_y: int,
                           steps: int = 10, duration: float = 0.5) -> bool:
        """滑动手势（模拟触屏滑动）"""
        try:
            await self._ensure_action_interval()
            
            logger.info(f"滑动手势: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
            
            # 计算每步的移动距离
            step_x = (end_x - start_x) / steps
            step_y = (end_y - start_y) / steps
            step_duration = duration / steps
            
            # 移动到起始位置并按下
            await self.move_mouse(start_x, start_y)
            pyautogui.mouseDown()
            
            try:
                # 逐步移动
                for i in range(1, steps + 1):
                    current_x = start_x + int(step_x * i)
                    current_y = start_y + int(step_y * i)
                    
                    pyautogui.moveTo(current_x, current_y, duration=step_duration)
                    await asyncio.sleep(0.01)  # 小延迟
                
            finally:
                # 释放鼠标
                pyautogui.mouseUp()
            
            self.last_action_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"滑动手势失败: {e}")
            return False
    
    async def tap_sequence(self, positions: List[Tuple[int, int]], 
                          interval: float = 0.5) -> bool:
        """连续点击序列"""
        try:
            logger.info(f"执行点击序列: {len(positions)}个位置")
            
            for i, (x, y) in enumerate(positions):
                success = await self.click_position(x, y)
                if not success:
                    logger.error(f"点击序列第{i+1}步失败")
                    return False
                
                if i < len(positions) - 1:  # 不是最后一个
                    await asyncio.sleep(interval)
            
            return True
            
        except Exception as e:
            logger.error(f"点击序列失败: {e}")
            return False
    
    async def wait_for_element(self, check_function, timeout: float = 10.0, 
                              check_interval: float = 0.5) -> bool:
        """等待元素出现"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if await check_function():
                    return True
                await asyncio.sleep(check_interval)
            
            logger.warning(f"等待元素超时: {timeout}秒")
            return False
            
        except Exception as e:
            logger.error(f"等待元素失败: {e}")
            return False
    
    async def simulate_human_behavior(self, action_type: str = 'random') -> bool:
        """模拟人类行为"""
        try:
            if action_type == 'random':
                # 随机选择一个行为
                behaviors = ['move_mouse', 'small_scroll', 'pause']
                action_type = random.choice(behaviors)
            
            if action_type == 'move_mouse':
                # 随机移动鼠标
                screen_width, screen_height = pyautogui.size()
                x = random.randint(100, screen_width - 100)
                y = random.randint(100, screen_height - 100)
                await self.move_mouse(x, y, duration=random.uniform(0.3, 0.8))
            
            elif action_type == 'small_scroll':
                # 小幅滚动
                current_x, current_y = pyautogui.position()
                await self.scroll(current_x, current_y, clicks=random.randint(1, 3))
            
            elif action_type == 'pause':
                # 随机暂停
                await asyncio.sleep(random.uniform(0.5, 2.0))
            
            return True
            
        except Exception as e:
            logger.error(f"模拟人类行为失败: {e}")
            return False
    
    async def _ensure_action_interval(self):
        """确保操作间隔"""
        current_time = time.time()
        elapsed = current_time - self.last_action_time
        
        if elapsed < self.min_action_interval:
            wait_time = self.min_action_interval - elapsed
            await asyncio.sleep(wait_time)
    
    def get_screen_size(self) -> Tuple[int, int]:
        """获取屏幕尺寸"""
        return pyautogui.size()
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """获取当前鼠标位置"""
        return pyautogui.position()
    
    def set_action_interval(self, interval: float):
        """设置操作间隔"""
        self.min_action_interval = max(0.1, interval)
        logger.info(f"设置操作间隔: {self.min_action_interval}秒")

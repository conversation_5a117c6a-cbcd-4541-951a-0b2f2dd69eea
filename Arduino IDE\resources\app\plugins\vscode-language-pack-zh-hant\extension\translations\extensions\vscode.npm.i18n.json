{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Could not find a valid npm script at the selection.": "在選取範圍中找不到有效的 npm 指令碼。", "Debug": "偵錯", "Debug Script": "對指令碼偵錯", "Default bower.json": "預設 bower.json", "Default package.json": "預設 package.json", "Do not show again": "不要再顯示", "Latest version: {0}": "最新版本: {0}", "Latest version: {0} published {1}": "最新版本: {0} 發佈於 {1}", "Learn more": "深入了解", "Matches the most recent major version (1.x.x)": "符合最新主要版本 (1.x.x)", "Matches the most recent minor version (1.2.x)": "符合最新次要版本 (1.2.x)", "No scripts found.": "找不到任何指令碼。", "Npm task detection: failed to parse the file {0}": "Npm 工作偵測: 無法剖析檔案 {0}", "Request to the NPM repository failed: {0}": "對 NPM 儲存機制的要求失敗: {0}", "Request to the bower repository failed: {0}": "對 Bower 儲存機制的要求失敗: {0}", "Run Script": "執行指令碼", "Run the script as a task": "以工作形式執行指令碼", "Runs the script under the debugger": "在偵錯工具中執行指令碼", "The currently latest version of the package": "封裝目前的最新版本", "The setting \"npm.autoDetect\" is \"off\".": "設定 \"npm.autoDetect\" 為 \"off\"。", "Using {0} as the preferred package manager. Found multiple lockfiles for {1}.  To resolve this issue, delete the lockfiles that don't match your preferred package manager or change the setting \"npm.packageManager\" to a value other than \"auto\".": "使用 {0} 做為慣用的套件管理員。找到多個 {1} 的鎖定檔案。若要解決此問題，請刪除不符合您慣用套件管理員的鎖定檔案，或將設定 \"npm.packageManager\" 變更為 [自動] 以外的值。", "in {0}": "於 {0}", "latest": "最新", "now": "現在", "{0} day": "{0} 天", "{0} day ago": "{0} 天前", "{0} days": "{0} 天", "{0} days ago": "{0} 天前", "{0} hour": "{0} 小時", "{0} hour ago": "{0} 小時前", "{0} hours": "{0} 小時", "{0} hours ago": "{0} 小時前", "{0} hr": "{0} 小時", "{0} hr ago": "{0} 小時前", "{0} hrs": "{0} 小時", "{0} hrs ago": "{0} 小時前", "{0} min": "{0} 分鐘", "{0} min ago": "{0} 分鐘前", "{0} mins": "{0} 分鐘", "{0} mins ago": "{0} 分鐘前", "{0} minute": "{0} 分鐘", "{0} minute ago": "{0} 分鐘前", "{0} minutes": "{0} 分鐘", "{0} minutes ago": "{0} 分鐘前", "{0} mo": "{0} 個月", "{0} mo ago": "{0} 個月前", "{0} month": "{0} 個月", "{0} month ago": "{0} 個月前", "{0} months": "{0} 個月", "{0} months ago": "{0} 個月前", "{0} mos": "{0} 個月", "{0} mos ago": "{0} 個月前", "{0} sec": "{0} 秒", "{0} sec ago": "{0} 秒前", "{0} second": "{0} 秒", "{0} second ago": "{0} 秒前", "{0} seconds": "{0} 秒", "{0} seconds ago": "{0} 秒前", "{0} secs": "{0} 秒", "{0} secs ago": "{0} 秒前", "{0} week": "{0} 週", "{0} week ago": "{0} 週前", "{0} weeks": "{0} 週", "{0} weeks ago": "{0} 週前", "{0} wk": "{0} 週", "{0} wk ago": "{0} 週前", "{0} wks": "{0} 週", "{0} wks ago": "{0} 週前", "{0} year": "{0} 年", "{0} year ago": "{0} 年前", "{0} years": "{0} 年", "{0} years ago": "{0} 年前", "{0} yr": "{0} 年", "{0} yr ago": "{0} 年前", "{0} yrs": "{0} 年", "{0} yrs ago": "{0} 年前"}, "package": {"command.debug": "偵錯", "command.openScript": "開啟", "command.packageManager": "取得已設定的套件管理員", "command.refresh": "重新整理", "command.run": "執行", "command.runInstall": "執行安裝", "command.runScriptFromFolder": "執行資料夾中的 NPM 指令碼...", "command.runSelectedScript": "執行指令碼", "config.npm.autoDetect": "控制是否自動偵測 npm 指令碼。", "config.npm.enableRunFromFolder": "從 Explorer 操作功能表啟用包含在資料夾中的執行中 NPM 指令碼。", "config.npm.enableScriptExplorer": "當沒有最上層 'package.json' 檔案時，啟用 npm 指令碼的總管檢視。", "config.npm.exclude": "為應從自動指令碼偵測排除的資料夾設定 Glob 模式。", "config.npm.fetchOnlinePackageInfo": "從 https://registry.npmjs.org 和 https://registry.bower.io 擷取資料，以提供 npm 相依性的自動完成和動態顯示資訊功能。", "config.npm.packageManager": "用來執行指令碼的套件管理員。", "config.npm.packageManager.auto": "根據鎖定檔案及已安裝的套件管理員，自動偵測要用來執行指令碼的套件管理員。", "config.npm.packageManager.bun": "使用 bun 作為執行指令碼的套件管理員。", "config.npm.packageManager.npm": "使用 npm 作為執行指令碼的套件管理員。", "config.npm.packageManager.pnpm": "使用 pnpm 作為執行指令碼的套件管理員。", "config.npm.packageManager.yarn": "使用 yarn 作為執行指令碼的套件管理員。", "config.npm.runSilent": "以 `--silent` 選項執行 npm 命令。 ", "config.npm.scriptExplorerAction": "npm 指令碼總管中使用的預設按鍵動作:「開啟」或「執行」，預設為「開啟」。", "config.npm.scriptExplorerExclude": "規則運算式陣列，指出應從 NPM 指令碼檢視中排除的指令碼。", "config.npm.scriptHover": "使用指令碼的 'Run' 和 'Debug' 命令顯示暫留。", "description": "為 npm 指令碼新增工作支援的延伸模組。", "displayName": "VS Code 的 Npm 支援", "npm.parseError": "Npm 工作偵測: 無法剖析檔案 {0}", "taskdef.path": "提供指令碼 package.json 檔案的資料夾路徑。可以省略。", "taskdef.script": "要自訂的 npm 指令碼。", "view.name": "Npm 指令碼", "virtualWorkspaces": "虛擬工作區中無法使用需要執行 'npm' 命令的功能。", "workspaceTrust": "此延伸模組會執行需要信任執行的工作。"}}}
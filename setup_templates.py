#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板设置工具
用于创建和管理广告检测模板
"""

import cv2
import numpy as np
import os
import json
from typing import Dict, List, Tuple
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
from PIL import Image, ImageTk

class TemplateManager:
    """模板管理器"""
    
    def __init__(self):
        self.templates_dir = "templates"
        self.templates_info = {}
        self.current_screenshot = None
        
        # 创建模板目录
        os.makedirs(self.templates_dir, exist_ok=True)
        
        # 加载现有模板信息
        self.load_templates_info()
    
    def load_templates_info(self):
        """加载模板信息"""
        info_file = os.path.join(self.templates_dir, "templates_info.json")
        if os.path.exists(info_file):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    self.templates_info = json.load(f)
            except Exception as e:
                print(f"加载模板信息失败: {e}")
                self.templates_info = {}
    
    def save_templates_info(self):
        """保存模板信息"""
        info_file = os.path.join(self.templates_dir, "templates_info.json")
        try:
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(self.templates_info, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存模板信息失败: {e}")
    
    def capture_template_from_screen(self, template_name: str, description: str = ""):
        """从屏幕截图中选择模板区域"""
        try:
            # 截取全屏
            import mss
            with mss.mss() as sct:
                screenshot = sct.grab(sct.monitors[1])
                img_array = np.array(screenshot)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
            
            # 显示截图并让用户选择区域
            selected_region = self.select_region_gui(img_bgr)
            
            if selected_region:
                x, y, w, h = selected_region
                template = img_bgr[y:y+h, x:x+w]
                
                # 保存模板
                template_path = os.path.join(self.templates_dir, f"{template_name}.png")
                cv2.imwrite(template_path, template)
                
                # 保存模板信息
                self.templates_info[template_name] = {
                    "filename": f"{template_name}.png",
                    "description": description,
                    "size": [w, h],
                    "created_time": str(cv2.getTickCount()),
                    "type": "user_defined"
                }
                
                self.save_templates_info()
                print(f"模板已保存: {template_name}")
                return True
            
        except Exception as e:
            print(f"捕获模板失败: {e}")
            return False
    
    def select_region_gui(self, image: np.ndarray) -> Tuple[int, int, int, int]:
        """GUI选择区域"""
        # 这里简化实现，实际可以使用更复杂的GUI
        # 返回 (x, y, width, height)
        
        # 显示图像并等待用户点击
        cv2.namedWindow("Select Template Region", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("Select Template Region", 1200, 800)
        
        # 全局变量存储选择状态
        self.selection_start = None
        self.selection_end = None
        self.selecting = False
        
        def mouse_callback(event, x, y, flags, param):
            if event == cv2.EVENT_LBUTTONDOWN:
                self.selection_start = (x, y)
                self.selecting = True
            elif event == cv2.EVENT_MOUSEMOVE and self.selecting:
                temp_img = image.copy()
                cv2.rectangle(temp_img, self.selection_start, (x, y), (0, 255, 0), 2)
                cv2.imshow("Select Template Region", temp_img)
            elif event == cv2.EVENT_LBUTTONUP:
                self.selection_end = (x, y)
                self.selecting = False
        
        cv2.setMouseCallback("Select Template Region", mouse_callback)
        cv2.imshow("Select Template Region", image)
        
        print("请在图像中拖拽选择模板区域，按ESC取消，按ENTER确认")
        
        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                cv2.destroyAllWindows()
                return None
            elif key == 13:  # ENTER
                if self.selection_start and self.selection_end:
                    x1, y1 = self.selection_start
                    x2, y2 = self.selection_end
                    
                    x = min(x1, x2)
                    y = min(y1, y2)
                    w = abs(x2 - x1)
                    h = abs(y2 - y1)
                    
                    cv2.destroyAllWindows()
                    return (x, y, w, h)
        
        cv2.destroyAllWindows()
        return None
    
    def create_default_templates(self):
        """创建默认模板"""
        print("创建默认模板...")
        
        # 关闭按钮模板 (X形状)
        close_template = np.zeros((30, 30, 3), dtype=np.uint8)
        cv2.line(close_template, (5, 5), (25, 25), (255, 255, 255), 2)
        cv2.line(close_template, (25, 5), (5, 25), (255, 255, 255), 2)
        cv2.imwrite(os.path.join(self.templates_dir, "close_button.png"), close_template)
        
        # 跳过按钮模板
        skip_template = np.zeros((60, 80, 3), dtype=np.uint8)
        cv2.rectangle(skip_template, (5, 5), (75, 55), (100, 100, 100), -1)
        cv2.putText(skip_template, "Skip", (15, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.imwrite(os.path.join(self.templates_dir, "skip_button.png"), skip_template)
        
        # 观看广告按钮模板
        watch_template = np.zeros((50, 120, 3), dtype=np.uint8)
        cv2.rectangle(watch_template, (5, 5), (115, 45), (0, 150, 0), -1)
        cv2.putText(watch_template, "Watch", (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.imwrite(os.path.join(self.templates_dir, "watch_ad_button.png"), watch_template)
        
        # 更新模板信息
        default_templates = {
            "close_button": {
                "filename": "close_button.png",
                "description": "关闭按钮 (X形状)",
                "size": [30, 30],
                "type": "default"
            },
            "skip_button": {
                "filename": "skip_button.png", 
                "description": "跳过按钮",
                "size": [80, 60],
                "type": "default"
            },
            "watch_ad_button": {
                "filename": "watch_ad_button.png",
                "description": "观看广告按钮",
                "size": [120, 50],
                "type": "default"
            }
        }
        
        self.templates_info.update(default_templates)
        self.save_templates_info()
        
        print("默认模板创建完成")
    
    def test_template(self, template_name: str):
        """测试模板匹配"""
        try:
            template_path = os.path.join(self.templates_dir, f"{template_name}.png")
            if not os.path.exists(template_path):
                print(f"模板文件不存在: {template_path}")
                return
            
            # 读取模板
            template = cv2.imread(template_path)
            
            # 截取当前屏幕
            import mss
            with mss.mss() as sct:
                screenshot = sct.grab(sct.monitors[1])
                img_array = np.array(screenshot)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
            
            # 模板匹配
            result = cv2.matchTemplate(img_bgr, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            print(f"模板 {template_name} 匹配结果:")
            print(f"  最大匹配度: {max_val:.3f}")
            print(f"  匹配位置: {max_loc}")
            
            # 在图像上标记匹配位置
            if max_val > 0.7:
                h, w = template.shape[:2]
                top_left = max_loc
                bottom_right = (top_left[0] + w, top_left[1] + h)
                
                cv2.rectangle(img_bgr, top_left, bottom_right, (0, 255, 0), 2)
                cv2.putText(img_bgr, f"{template_name}: {max_val:.3f}", 
                           (top_left[0], top_left[1] - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示结果
            cv2.namedWindow("Template Test Result", cv2.WINDOW_NORMAL)
            cv2.resizeWindow("Template Test Result", 1200, 800)
            cv2.imshow("Template Test Result", img_bgr)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"测试模板失败: {e}")
    
    def list_templates(self):
        """列出所有模板"""
        print("\n=== 模板列表 ===")
        for name, info in self.templates_info.items():
            print(f"名称: {name}")
            print(f"  文件: {info['filename']}")
            print(f"  描述: {info['description']}")
            print(f"  尺寸: {info['size']}")
            print(f"  类型: {info['type']}")
            print()
    
    def delete_template(self, template_name: str):
        """删除模板"""
        try:
            if template_name in self.templates_info:
                filename = self.templates_info[template_name]['filename']
                file_path = os.path.join(self.templates_dir, filename)
                
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                del self.templates_info[template_name]
                self.save_templates_info()
                
                print(f"模板已删除: {template_name}")
            else:
                print(f"模板不存在: {template_name}")
                
        except Exception as e:
            print(f"删除模板失败: {e}")


def main():
    """主函数"""
    manager = TemplateManager()
    
    while True:
        print("\n=== 模板管理工具 ===")
        print("1. 创建默认模板")
        print("2. 从屏幕截图创建模板")
        print("3. 测试模板匹配")
        print("4. 列出所有模板")
        print("5. 删除模板")
        print("0. 退出")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == "1":
            manager.create_default_templates()
        
        elif choice == "2":
            name = input("输入模板名称: ").strip()
            if name:
                description = input("输入模板描述 (可选): ").strip()
                manager.capture_template_from_screen(name, description)
        
        elif choice == "3":
            name = input("输入要测试的模板名称: ").strip()
            if name:
                manager.test_template(name)
        
        elif choice == "4":
            manager.list_templates()
        
        elif choice == "5":
            name = input("输入要删除的模板名称: ").strip()
            if name:
                confirm = input(f"确认删除模板 '{name}'? (y/N): ").strip().lower()
                if confirm == 'y':
                    manager.delete_template(name)
        
        elif choice == "0":
            break
        
        else:
            print("无效选择，请重试")


if __name__ == "__main__":
    main()

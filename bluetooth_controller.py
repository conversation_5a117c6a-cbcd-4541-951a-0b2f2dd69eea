#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32-C3蓝牙控制器
负责与ESP32-C3设备的蓝牙通信
"""

import asyncio
import logging
import json
from typing import Optional, Dict, Any, Callable
from bleak import BleakClient, BleakScanner
from bleak.backends.characteristic import BleakGATTCharacteristic

logger = logging.getLogger(__name__)


class BluetoothController:
    """ESP32-C3蓝牙控制器"""
    
    # ESP32-C3 蓝牙服务和特征UUID (需要根据实际ESP32程序配置)
    SERVICE_UUID = "12345678-1234-1234-1234-123456789abc"
    COMMAND_CHAR_UUID = "*************-4321-4321-cba987654321"
    STATUS_CHAR_UUID = "11111111-**************-************"
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client: Optional[BleakClient] = None
        self.device_address: Optional[str] = None
        self.connected = False
        self.status_callback: Optional[Callable] = None
        
    async def scan_devices(self, timeout: int = 10) -> list:
        """扫描附近的蓝牙设备"""
        logger.info("开始扫描蓝牙设备...")
        
        try:
            devices = await BleakScanner.discover(timeout=timeout)
            esp32_devices = []
            
            for device in devices:
                # 查找ESP32设备 (可以根据设备名称或MAC地址过滤)
                if device.name and ("ESP32" in device.name or "蓝牙" in device.name):
                    esp32_devices.append({
                        'name': device.name,
                        'address': device.address,
                        'rssi': device.rssi
                    })
                    logger.info(f"发现ESP32设备: {device.name} ({device.address})")
            
            return esp32_devices
            
        except Exception as e:
            logger.error(f"扫描设备失败: {e}")
            return []
    
    async def connect(self, device_address: Optional[str] = None) -> bool:
        """连接到ESP32设备"""
        try:
            # 如果没有指定地址，先扫描设备
            if not device_address:
                devices = await self.scan_devices()
                if not devices:
                    logger.error("未找到ESP32设备")
                    return False
                
                # 选择信号最强的设备
                device_address = max(devices, key=lambda x: x['rssi'])['address']
                logger.info(f"自动选择设备: {device_address}")
            
            self.device_address = device_address
            
            # 创建客户端并连接
            self.client = BleakClient(device_address)
            await self.client.connect()
            
            if self.client.is_connected:
                logger.info(f"成功连接到设备: {device_address}")
                self.connected = True
                
                # 订阅状态通知
                await self.subscribe_notifications()
                
                # 发送连接确认
                await self.send_command("CONNECT_OK")
                
                return True
            else:
                logger.error("连接失败")
                return False
                
        except Exception as e:
            logger.error(f"连接设备失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.client and self.client.is_connected:
                await self.send_command("DISCONNECT")
                await self.client.disconnect()
                logger.info("已断开蓝牙连接")
            
            self.connected = False
            self.client = None
            
        except Exception as e:
            logger.error(f"断开连接失败: {e}")
    
    async def send_command(self, command: str, data: Optional[Dict] = None) -> bool:
        """发送命令到ESP32"""
        if not self.connected or not self.client:
            logger.error("设备未连接")
            return False
        
        try:
            # 构造命令数据
            command_data = {
                'command': command,
                'timestamp': asyncio.get_event_loop().time(),
                'data': data or {}
            }
            
            # 转换为JSON字符串
            json_data = json.dumps(command_data, ensure_ascii=False)
            
            # 发送数据
            await self.client.write_gatt_char(
                self.COMMAND_CHAR_UUID, 
                json_data.encode('utf-8')
            )
            
            logger.debug(f"发送命令: {command}")
            return True
            
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False
    
    async def subscribe_notifications(self):
        """订阅ESP32状态通知"""
        try:
            await self.client.start_notify(
                self.STATUS_CHAR_UUID, 
                self._notification_handler
            )
            logger.info("已订阅状态通知")
            
        except Exception as e:
            logger.error(f"订阅通知失败: {e}")
    
    def _notification_handler(self, characteristic: BleakGATTCharacteristic, data: bytearray):
        """处理ESP32发送的通知"""
        try:
            # 解析JSON数据
            json_str = data.decode('utf-8')
            status_data = json.loads(json_str)
            
            logger.debug(f"收到状态通知: {status_data}")
            
            # 处理不同类型的状态
            status_type = status_data.get('type')
            
            if status_type == 'battery_level':
                self._handle_battery_status(status_data)
            elif status_type == 'sensor_data':
                self._handle_sensor_data(status_data)
            elif status_type == 'error':
                self._handle_error(status_data)
            
            # 调用外部回调函数
            if self.status_callback:
                self.status_callback(status_data)
                
        except Exception as e:
            logger.error(f"处理通知失败: {e}")
    
    def _handle_battery_status(self, data: Dict):
        """处理电池状态"""
        battery_level = data.get('level', 0)
        if battery_level < 20:
            logger.warning(f"ESP32电池电量低: {battery_level}%")
    
    def _handle_sensor_data(self, data: Dict):
        """处理传感器数据"""
        # 可以处理ESP32上的传感器数据，如加速度计、陀螺仪等
        pass
    
    def _handle_error(self, data: Dict):
        """处理错误信息"""
        error_msg = data.get('message', '未知错误')
        logger.error(f"ESP32报告错误: {error_msg}")
    
    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
    
    async def get_device_info(self) -> Optional[Dict]:
        """获取设备信息"""
        if not self.connected or not self.client:
            return None
        
        try:
            # 读取设备信息
            device_info = {}
            
            # 可以读取设备名称、固件版本等信息
            # 这需要ESP32端实现相应的特征
            
            return device_info
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return None
    
    async def send_click_command(self, x: int, y: int) -> bool:
        """发送点击命令"""
        return await self.send_command("CLICK", {"x": x, "y": y})
    
    async def send_swipe_command(self, start_x: int, start_y: int, end_x: int, end_y: int) -> bool:
        """发送滑动命令"""
        return await self.send_command("SWIPE", {
            "start_x": start_x,
            "start_y": start_y,
            "end_x": end_x,
            "end_y": end_y
        })
    
    async def send_vibration_command(self, duration: int = 100) -> bool:
        """发送震动命令"""
        return await self.send_command("VIBRATE", {"duration": duration})
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected and self.client and self.client.is_connected

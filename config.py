#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含所有系统配置参数
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_data = {}
        
        # 默认配置
        self.default_config = {
            "bluetooth": {
                "device_name": "ESP32-C3",
                "service_uuid": "12345678-1234-1234-1234-123456789abc",
                "command_char_uuid": "*************-4321-4321-cba987654321",
                "status_char_uuid": "11111111-**************-************",
                "scan_timeout": 10,
                "connection_timeout": 30,
                "auto_reconnect": True,
                "reconnect_interval": 5
            },
            "automation": {
                "cycle_interval": 2.0,
                "default_ad_duration": 30,
                "max_ad_duration": 60,
                "action_interval": 0.5,
                "screenshot_interval": 1.0,
                "max_retry_attempts": 3,
                "retry_delay": 1.0
            },
            "detection": {
                "confidence_threshold": 0.7,
                "template_match_threshold": 0.8,
                "color_tolerance": 30,
                "min_button_size": 100,
                "max_button_size": 10000,
                "enable_debug_output": False,
                "save_detection_images": False
            },
            "screen": {
                "capture_region": None,  # None表示全屏
                "resize_factor": 1.0,
                "enable_preprocessing": True,
                "preprocessing_operations": ["resize", "denoise"]
            },
            "tasks": {
                "platforms": [
                    {
                        "name": "抖音极速版",
                        "package_name": "com.ss.android.ugc.aweme.lite",
                        "ad_patterns": ["video_ad", "banner_ad"],
                        "reward_per_ad": 10,
                        "max_ads_per_session": 20
                    },
                    {
                        "name": "快手极速版", 
                        "package_name": "com.kuaishou.nebula",
                        "ad_patterns": ["video_ad", "popup_ad"],
                        "reward_per_ad": 15,
                        "max_ads_per_session": 15
                    }
                ],
                "daily_limits": {
                    "max_ads": 100,
                    "max_runtime_hours": 8,
                    "break_interval_minutes": 30,
                    "break_duration_minutes": 10
                }
            },
            "safety": {
                "enable_failsafe": True,
                "failsafe_position": [0, 0],  # 鼠标移到左上角停止
                "max_continuous_failures": 5,
                "emergency_stop_key": "ctrl+alt+q",
                "human_behavior_simulation": True,
                "random_delays": True,
                "delay_range": [0.5, 2.0]
            },
            "logging": {
                "level": "INFO",
                "file": "automation.log",
                "max_file_size": "10MB",
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        }
        
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"配置文件已加载: {self.config_file}")
            else:
                # 使用默认配置并保存
                self.config_data = self.default_config.copy()
                self.save_config()
                logger.info("使用默认配置并已保存")
            
            return True
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config_data = self.default_config.copy()
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
            logger.info(f"配置文件已保存: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key_path: str, default=None) -> Any:
        """获取配置值（支持点号分隔的路径）"""
        try:
            keys = key_path.split('.')
            value = self.config_data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """设置配置值（支持点号分隔的路径）"""
        try:
            keys = key_path.split('.')
            config = self.config_data
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            
            logger.info(f"配置已更新: {key_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
            return False
    
    def update(self, updates: Dict[str, Any]) -> bool:
        """批量更新配置"""
        try:
            for key_path, value in updates.items():
                self.set(key_path, value)
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"批量更新配置失败: {e}")
            return False
    
    @property
    def bluetooth_config(self) -> Dict[str, Any]:
        """蓝牙配置"""
        return self.get('bluetooth', {})
    
    @property
    def automation_config(self) -> Dict[str, Any]:
        """自动化配置"""
        return self.get('automation', {})
    
    @property
    def detection_config(self) -> Dict[str, Any]:
        """检测配置"""
        return self.get('detection', {})
    
    @property
    def screen_config(self) -> Dict[str, Any]:
        """屏幕配置"""
        return self.get('screen', {})
    
    @property
    def task_config(self) -> Dict[str, Any]:
        """任务配置"""
        return self.get('tasks', {})
    
    @property
    def safety_config(self) -> Dict[str, Any]:
        """安全配置"""
        return self.get('safety', {})
    
    @property
    def cycle_interval(self) -> float:
        """循环间隔"""
        return self.get('automation.cycle_interval', 2.0)
    
    @property
    def default_ad_duration(self) -> int:
        """默认广告时长"""
        return self.get('automation.default_ad_duration', 30)
    
    @property
    def confidence_threshold(self) -> float:
        """置信度阈值"""
        return self.get('detection.confidence_threshold', 0.7)
    
    def get_platform_config(self, platform_name: str) -> Optional[Dict[str, Any]]:
        """获取特定平台配置"""
        platforms = self.get('tasks.platforms', [])
        for platform in platforms:
            if platform.get('name') == platform_name:
                return platform
        return None
    
    def add_platform(self, platform_config: Dict[str, Any]) -> bool:
        """添加平台配置"""
        try:
            platforms = self.get('tasks.platforms', [])
            platforms.append(platform_config)
            return self.set('tasks.platforms', platforms)
            
        except Exception as e:
            logger.error(f"添加平台配置失败: {e}")
            return False
    
    def remove_platform(self, platform_name: str) -> bool:
        """移除平台配置"""
        try:
            platforms = self.get('tasks.platforms', [])
            platforms = [p for p in platforms if p.get('name') != platform_name]
            return self.set('tasks.platforms', platforms)
            
        except Exception as e:
            logger.error(f"移除平台配置失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查必要的配置项
            required_keys = [
                'bluetooth.service_uuid',
                'automation.cycle_interval',
                'detection.confidence_threshold'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    logger.error(f"缺少必要配置: {key}")
                    return False
            
            # 检查数值范围
            if not (0.1 <= self.cycle_interval <= 60):
                logger.error("cycle_interval 必须在 0.1-60 秒之间")
                return False
            
            if not (0.1 <= self.confidence_threshold <= 1.0):
                logger.error("confidence_threshold 必须在 0.1-1.0 之间")
                return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        try:
            self.config_data = self.default_config.copy()
            return self.save_config()
            
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            return False
    
    def export_config(self, filename: str) -> bool:
        """导出配置到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
            logger.info(f"配置已导出到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, filename: str) -> bool:
        """从文件导入配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            self.config_data.update(imported_config)
            logger.info(f"配置已从文件导入: {filename}")
            return self.save_config()
            
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False

package com.bluetooth.automation;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.util.Log;

import org.opencv.android.OpenCVLoaderCallback;
import org.opencv.android.OpenCVLoaderCallbackInterface;
import org.opencv.android.Utils;
import org.opencv.core.Core;
import org.opencv.core.CvType;
import org.opencv.core.Mat;
import org.opencv.core.MatOfPoint;
import org.opencv.core.Rect;
import org.opencv.core.Scalar;
import org.opencv.core.Size;
import org.opencv.imgproc.Imgproc;

import java.util.ArrayList;
import java.util.List;

/**
 * 广告检测服务
 * 使用OpenCV进行图像识别和广告检测
 */
public class AdDetectionService {
    
    private static final String TAG = "AdDetectionService";
    
    private Context context;
    private boolean isOpenCVInitialized = false;
    
    // 检测参数
    private static final double CONFIDENCE_THRESHOLD = 0.7;
    private static final int MIN_BUTTON_SIZE = 50;
    private static final int MAX_BUTTON_SIZE = 300;
    
    // 广告相关颜色 (HSV格式)
    private static final Scalar CLOSE_BUTTON_COLOR_LOWER = new Scalar(0, 0, 200);
    private static final Scalar CLOSE_BUTTON_COLOR_UPPER = new Scalar(180, 30, 255);
    
    public interface DetectionCallback {
        void onAdDetected(AdInfo adInfo);
        void onError(String error);
    }
    
    /**
     * 广告信息类
     */
    public static class AdInfo {
        public boolean hasAd = false;
        public String type = "";
        public double confidence = 0.0;
        public Point position = null;
        public Point closeButtonPosition = null;
        public int reward = 10;
        public int duration = 30;
        
        public AdInfo() {}
        
        public AdInfo(boolean hasAd, String type, double confidence) {
            this.hasAd = hasAd;
            this.type = type;
            this.confidence = confidence;
        }
    }
    
    public AdDetectionService(Context context) {
        this.context = context;
        initializeOpenCV();
    }
    
    /**
     * 初始化OpenCV
     */
    private void initializeOpenCV() {
        OpenCVLoaderCallback loaderCallback = new OpenCVLoaderCallback() {
            @Override
            public void onManagerConnected(int status) {
                switch (status) {
                    case OpenCVLoaderCallbackInterface.SUCCESS:
                        Log.d(TAG, "OpenCV初始化成功");
                        isOpenCVInitialized = true;
                        break;
                    default:
                        Log.e(TAG, "OpenCV初始化失败");
                        break;
                }
            }
            
            @Override
            public void onPackageInstall(int operation, String packageName) {
                // OpenCV Manager安装回调
            }
        };
        
        // 这里需要调用OpenCV的初始化方法
        // 实际使用时需要添加OpenCV Android SDK
    }
    
    /**
     * 检测广告
     */
    public void detectAd(Bitmap screenshot, DetectionCallback callback) {
        if (!isOpenCVInitialized) {
            callback.onError("OpenCV未初始化");
            return;
        }
        
        if (screenshot == null) {
            callback.onError("截图为空");
            return;
        }
        
        new Thread(() -> {
            try {
                AdInfo adInfo = performAdDetection(screenshot);
                callback.onAdDetected(adInfo);
            } catch (Exception e) {
                Log.e(TAG, "广告检测失败", e);
                callback.onError("检测失败: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 执行广告检测
     */
    private AdInfo performAdDetection(Bitmap screenshot) {
        AdInfo adInfo = new AdInfo();
        
        // 转换为OpenCV Mat
        Mat image = new Mat();
        Utils.bitmapToMat(screenshot, image);
        
        // 检测视频广告
        AdInfo videoAdInfo = detectVideoAd(image);
        if (videoAdInfo.hasAd) {
            return videoAdInfo;
        }
        
        // 检测横幅广告
        AdInfo bannerAdInfo = detectBannerAd(image);
        if (bannerAdInfo.hasAd) {
            return bannerAdInfo;
        }
        
        // 检测弹窗广告
        AdInfo popupAdInfo = detectPopupAd(image);
        if (popupAdInfo.hasAd) {
            return popupAdInfo;
        }
        
        // 查找关闭按钮
        Point closeButton = findCloseButton(image);
        if (closeButton != null) {
            adInfo.hasAd = true;
            adInfo.type = "unknown_ad";
            adInfo.confidence = 0.6;
            adInfo.closeButtonPosition = closeButton;
        }
        
        return adInfo;
    }
    
    /**
     * 检测视频广告
     */
    private AdInfo detectVideoAd(Mat image) {
        AdInfo adInfo = new AdInfo();
        
        try {
            // 检测播放按钮（圆形）
            Mat gray = new Mat();
            Imgproc.cvtColor(image, gray, Imgproc.COLOR_BGR2GRAY);
            
            Mat circles = new Mat();
            Imgproc.HoughCircles(gray, circles, Imgproc.HOUGH_GRADIENT, 1, 50,
                100, 30, 20, 100);
            
            if (circles.cols() > 0) {
                adInfo.hasAd = true;
                adInfo.type = "video_ad";
                adInfo.confidence = 0.8;
                
                // 获取第一个圆的位置
                float[] circleData = new float[3];
                circles.get(0, 0, circleData);
                adInfo.position = new Point((int)circleData[0], (int)circleData[1]);
            }
            
            // 检测倒计时文本
            if (detectCountdownText(image)) {
                adInfo.hasAd = true;
                adInfo.type = "video_ad";
                adInfo.confidence = Math.max(adInfo.confidence, 0.9);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "视频广告检测失败", e);
        }
        
        return adInfo;
    }
    
    /**
     * 检测横幅广告
     */
    private AdInfo detectBannerAd(Mat image) {
        AdInfo adInfo = new AdInfo();
        
        try {
            int height = image.rows();
            int width = image.cols();
            
            // 检查顶部区域
            Rect topRegion = new Rect(0, 0, width, height / 4);
            Mat topMat = new Mat(image, topRegion);
            
            if (isAdRegion(topMat)) {
                adInfo.hasAd = true;
                adInfo.type = "banner_ad";
                adInfo.confidence = 0.7;
                adInfo.position = new Point(width / 2, height / 8);
            }
            
            // 检查底部区域
            if (!adInfo.hasAd) {
                Rect bottomRegion = new Rect(0, 3 * height / 4, width, height / 4);
                Mat bottomMat = new Mat(image, bottomRegion);
                
                if (isAdRegion(bottomMat)) {
                    adInfo.hasAd = true;
                    adInfo.type = "banner_ad";
                    adInfo.confidence = 0.7;
                    adInfo.position = new Point(width / 2, 7 * height / 8);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "横幅广告检测失败", e);
        }
        
        return adInfo;
    }
    
    /**
     * 检测弹窗广告
     */
    private AdInfo detectPopupAd(Mat image) {
        AdInfo adInfo = new AdInfo();
        
        try {
            int height = image.rows();
            int width = image.cols();
            
            // 检查中央区域
            Rect centerRegion = new Rect(width / 4, height / 4, width / 2, height / 2);
            Mat centerMat = new Mat(image, centerRegion);
            
            if (isPopupRegion(centerMat)) {
                adInfo.hasAd = true;
                adInfo.type = "popup_ad";
                adInfo.confidence = 0.8;
                adInfo.position = new Point(width / 2, height / 2);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "弹窗广告检测失败", e);
        }
        
        return adInfo;
    }
    
    /**
     * 检测倒计时文本
     */
    private boolean detectCountdownText(Mat image) {
        try {
            // 简化实现：检测右上角的白色文本区域
            int height = image.rows();
            int width = image.cols();
            
            Rect countdownRegion = new Rect(2 * width / 3, 0, width / 3, height / 6);
            Mat countdownMat = new Mat(image, countdownRegion);
            
            Mat gray = new Mat();
            Imgproc.cvtColor(countdownMat, gray, Imgproc.COLOR_BGR2GRAY);
            
            Mat thresh = new Mat();
            Imgproc.threshold(gray, thresh, 200, 255, Imgproc.THRESH_BINARY);
            
            // 计算白色像素比例
            Scalar whitePixels = Core.sumElems(thresh);
            double whiteRatio = whitePixels.val[0] / (thresh.rows() * thresh.cols() * 255);
            
            return whiteRatio > 0.1; // 如果白色像素超过10%，可能是倒计时
            
        } catch (Exception e) {
            Log.e(TAG, "倒计时检测失败", e);
            return false;
        }
    }
    
    /**
     * 判断是否为广告区域
     */
    private boolean isAdRegion(Mat region) {
        try {
            // 检测颜色丰富度
            Mat hsv = new Mat();
            Imgproc.cvtColor(region, hsv, Imgproc.COLOR_BGR2HSV);
            
            // 计算直方图
            List<Mat> hsvPlanes = new ArrayList<>();
            Core.split(hsv, hsvPlanes);
            
            Mat hist = new Mat();
            Imgproc.calcHist(hsvPlanes.subList(0, 2), new MatOfPoint(0, 1), 
                new Mat(), hist, new MatOfPoint(50, 60), 
                new MatOfPoint(0, 180, 0, 256));
            
            // 计算颜色多样性
            int nonZeroCount = Core.countNonZero(hist);
            double colorDiversity = (double) nonZeroCount / hist.total();
            
            // 检测边缘密度
            Mat gray = new Mat();
            Imgproc.cvtColor(region, gray, Imgproc.COLOR_BGR2GRAY);
            
            Mat edges = new Mat();
            Imgproc.Canny(gray, edges, 50, 150);
            
            int edgePixels = Core.countNonZero(edges);
            double edgeDensity = (double) edgePixels / edges.total();
            
            // 综合评分
            double adScore = colorDiversity * 0.6 + edgeDensity * 0.4;
            
            return adScore > 0.3;
            
        } catch (Exception e) {
            Log.e(TAG, "广告区域检测失败", e);
            return false;
        }
    }
    
    /**
     * 判断是否为弹窗区域
     */
    private boolean isPopupRegion(Mat region) {
        try {
            // 检测矩形边框
            Mat gray = new Mat();
            Imgproc.cvtColor(region, gray, Imgproc.COLOR_BGR2GRAY);
            
            Mat edges = new Mat();
            Imgproc.Canny(gray, edges, 50, 150);
            
            List<MatOfPoint> contours = new ArrayList<>();
            Mat hierarchy = new Mat();
            Imgproc.findContours(edges, contours, hierarchy, 
                Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);
            
            for (MatOfPoint contour : contours) {
                double area = Imgproc.contourArea(contour);
                if (area > 1000) { // 足够大的区域
                    Rect boundingRect = Imgproc.boundingRect(contour);
                    double rectangularity = area / (boundingRect.width * boundingRect.height);
                    
                    if (rectangularity > 0.8) { // 高矩形度
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "弹窗区域检测失败", e);
            return false;
        }
    }
    
    /**
     * 查找关闭按钮
     */
    private Point findCloseButton(Mat image) {
        try {
            // 方法1：颜色检测
            Point colorButton = findCloseButtonByColor(image);
            if (colorButton != null) {
                return colorButton;
            }
            
            // 方法2：形状检测
            Point shapeButton = findCloseButtonByShape(image);
            if (shapeButton != null) {
                return shapeButton;
            }
            
            // 方法3：位置检测
            return findCloseButtonByPosition(image);
            
        } catch (Exception e) {
            Log.e(TAG, "关闭按钮检测失败", e);
            return null;
        }
    }
    
    /**
     * 通过颜色查找关闭按钮
     */
    private Point findCloseButtonByColor(Mat image) {
        try {
            Mat hsv = new Mat();
            Imgproc.cvtColor(image, hsv, Imgproc.COLOR_BGR2HSV);
            
            Mat mask = new Mat();
            Core.inRange(hsv, CLOSE_BUTTON_COLOR_LOWER, CLOSE_BUTTON_COLOR_UPPER, mask);
            
            List<MatOfPoint> contours = new ArrayList<>();
            Mat hierarchy = new Mat();
            Imgproc.findContours(mask, contours, hierarchy, 
                Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);
            
            for (MatOfPoint contour : contours) {
                double area = Imgproc.contourArea(contour);
                if (area >= MIN_BUTTON_SIZE && area <= MAX_BUTTON_SIZE) {
                    Rect boundingRect = Imgproc.boundingRect(contour);
                    
                    // 检查长宽比
                    double aspectRatio = (double) boundingRect.width / boundingRect.height;
                    if (aspectRatio > 0.5 && aspectRatio < 2.0) {
                        return new Point(
                            boundingRect.x + boundingRect.width / 2,
                            boundingRect.y + boundingRect.height / 2
                        );
                    }
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "颜色检测关闭按钮失败", e);
        }
        
        return null;
    }
    
    /**
     * 通过形状查找关闭按钮
     */
    private Point findCloseButtonByShape(Mat image) {
        try {
            Mat gray = new Mat();
            Imgproc.cvtColor(image, gray, Imgproc.COLOR_BGR2GRAY);
            
            Mat edges = new Mat();
            Imgproc.Canny(gray, edges, 50, 150);
            
            // 查找直线（X形状的组成部分）
            Mat lines = new Mat();
            Imgproc.HoughLinesP(edges, lines, 1, Math.PI / 180, 50, 30, 10);
            
            // 分析直线组合，查找X形状
            // 这里简化实现
            if (lines.rows() >= 2) {
                // 如果找到足够的直线，可能是X形状
                return new Point(image.cols() - 50, 50); // 返回右上角位置
            }
            
        } catch (Exception e) {
            Log.e(TAG, "形状检测关闭按钮失败", e);
        }
        
        return null;
    }
    
    /**
     * 通过位置查找关闭按钮
     */
    private Point findCloseButtonByPosition(Mat image) {
        try {
            int width = image.cols();
            int height = image.rows();
            
            // 常见的关闭按钮位置
            Point[] commonPositions = {
                new Point(width - 50, 50),      // 右上角
                new Point(width - 100, 50),     // 右上角偏左
                new Point(width - 50, 100),     // 右上角偏下
            };
            
            for (Point pos : commonPositions) {
                if (isLikelyCloseButton(image, pos)) {
                    return pos;
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "位置检测关闭按钮失败", e);
        }
        
        return null;
    }
    
    /**
     * 检查指定位置是否可能是关闭按钮
     */
    private boolean isLikelyCloseButton(Mat image, Point position) {
        try {
            int size = 30;
            int x = (int) Math.max(0, position.x - size / 2);
            int y = (int) Math.max(0, position.y - size / 2);
            int width = Math.min(size, image.cols() - x);
            int height = Math.min(size, image.rows() - y);
            
            if (width <= 0 || height <= 0) {
                return false;
            }
            
            Rect region = new Rect(x, y, width, height);
            Mat regionMat = new Mat(image, region);
            
            Mat gray = new Mat();
            Imgproc.cvtColor(regionMat, gray, Imgproc.COLOR_BGR2GRAY);
            
            Mat edges = new Mat();
            Imgproc.Canny(gray, edges, 50, 150);
            
            int edgePixels = Core.countNonZero(edges);
            double edgeRatio = (double) edgePixels / edges.total();
            
            return edgeRatio > 0.1; // 如果边缘像素超过10%，可能是按钮
            
        } catch (Exception e) {
            Log.e(TAG, "检查关闭按钮失败", e);
            return false;
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        // 清理OpenCV资源
        Log.d(TAG, "广告检测服务已释放");
    }
    
    /**
     * 设置检测参数
     */
    public void setDetectionParameters(double confidenceThreshold, int minButtonSize, int maxButtonSize) {
        // 可以动态调整检测参数
    }
    
    /**
     * 获取检测统计信息
     */
    public DetectionStats getStats() {
        return new DetectionStats();
    }
    
    /**
     * 检测统计信息类
     */
    public static class DetectionStats {
        public int totalDetections = 0;
        public int successfulDetections = 0;
        public double averageConfidence = 0.0;
        public long averageProcessingTime = 0;
    }
}

package com.bluetooth.automation;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.Image;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Handler;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.util.Log;

import java.nio.ByteBuffer;

/**
 * 屏幕截图服务
 * 负责捕获屏幕内容
 */
public class ScreenCaptureService {
    
    private static final String TAG = "ScreenCaptureService";
    private static final int REQUEST_MEDIA_PROJECTION = 1000;
    
    private Context context;
    private MediaProjectionManager projectionManager;
    private MediaProjection mediaProjection;
    private VirtualDisplay virtualDisplay;
    private ImageReader imageReader;
    
    private int screenWidth;
    private int screenHeight;
    private int screenDensity;
    
    private Handler handler;
    private boolean isCapturing = false;
    
    public interface ScreenshotCallback {
        void onScreenshotTaken(Bitmap screenshot);
        void onError(String error);
    }
    
    public ScreenCaptureService(Context context) {
        this.context = context;
        this.handler = new Handler(Looper.getMainLooper());
        
        // 获取屏幕参数
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        screenWidth = metrics.widthPixels;
        screenHeight = metrics.heightPixels;
        screenDensity = metrics.densityDpi;
        
        // 初始化MediaProjection
        projectionManager = (MediaProjectionManager) 
            context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
    }
    
    /**
     * 请求屏幕录制权限
     */
    public void requestScreenCapturePermission(Activity activity) {
        Intent intent = projectionManager.createScreenCaptureIntent();
        activity.startActivityForResult(intent, REQUEST_MEDIA_PROJECTION);
    }
    
    /**
     * 处理权限请求结果
     */
    public void handlePermissionResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_MEDIA_PROJECTION) {
            if (resultCode == Activity.RESULT_OK) {
                mediaProjection = projectionManager.getMediaProjection(resultCode, data);
                setupImageReader();
                Log.d(TAG, "屏幕录制权限已获取");
            } else {
                Log.e(TAG, "屏幕录制权限被拒绝");
            }
        }
    }
    
    /**
     * 设置ImageReader
     */
    private void setupImageReader() {
        imageReader = ImageReader.newInstance(screenWidth, screenHeight, 
            PixelFormat.RGBA_8888, 2);
        
        virtualDisplay = mediaProjection.createVirtualDisplay(
            "ScreenCapture",
            screenWidth, screenHeight, screenDensity,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
            imageReader.getSurface(), null, null);
        
        Log.d(TAG, "ImageReader设置完成");
    }
    
    /**
     * 捕获屏幕
     */
    public void captureScreen(ScreenshotCallback callback) {
        if (mediaProjection == null || imageReader == null) {
            callback.onError("屏幕录制未初始化");
            return;
        }
        
        if (isCapturing) {
            callback.onError("正在截图中，请稍候");
            return;
        }
        
        isCapturing = true;
        
        imageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
            @Override
            public void onImageAvailable(ImageReader reader) {
                Image image = null;
                try {
                    image = reader.acquireLatestImage();
                    if (image != null) {
                        Bitmap bitmap = imageToBitmap(image);
                        handler.post(() -> {
                            callback.onScreenshotTaken(bitmap);
                            isCapturing = false;
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "截图失败", e);
                    handler.post(() -> {
                        callback.onError("截图失败: " + e.getMessage());
                        isCapturing = false;
                    });
                } finally {
                    if (image != null) {
                        image.close();
                    }
                }
            }
        }, handler);
    }
    
    /**
     * 将Image转换为Bitmap
     */
    private Bitmap imageToBitmap(Image image) {
        Image.Plane[] planes = image.getPlanes();
        ByteBuffer buffer = planes[0].getBuffer();
        int pixelStride = planes[0].getPixelStride();
        int rowStride = planes[0].getRowStride();
        int rowPadding = rowStride - pixelStride * screenWidth;
        
        Bitmap bitmap = Bitmap.createBitmap(
            screenWidth + rowPadding / pixelStride, 
            screenHeight, 
            Bitmap.Config.ARGB_8888);
        
        bitmap.copyPixelsFromBuffer(buffer);
        
        // 如果有padding，需要裁剪
        if (rowPadding != 0) {
            bitmap = Bitmap.createBitmap(bitmap, 0, 0, screenWidth, screenHeight);
        }
        
        return bitmap;
    }
    
    /**
     * 连续截图
     */
    public void startContinuousCapture(ScreenshotCallback callback, int intervalMs) {
        if (mediaProjection == null || imageReader == null) {
            callback.onError("屏幕录制未初始化");
            return;
        }
        
        Handler captureHandler = new Handler(Looper.getMainLooper());
        
        Runnable captureRunnable = new Runnable() {
            @Override
            public void run() {
                captureScreen(new ScreenshotCallback() {
                    @Override
                    public void onScreenshotTaken(Bitmap screenshot) {
                        callback.onScreenshotTaken(screenshot);
                        // 继续下一次截图
                        captureHandler.postDelayed(this, intervalMs);
                    }
                    
                    @Override
                    public void onError(String error) {
                        callback.onError(error);
                    }
                });
            }
        };
        
        captureHandler.post(captureRunnable);
    }
    
    /**
     * 停止连续截图
     */
    public void stopContinuousCapture() {
        handler.removeCallbacksAndMessages(null);
        isCapturing = false;
    }
    
    /**
     * 截图并保存到文件
     */
    public void captureAndSave(String filename, ScreenshotCallback callback) {
        captureScreen(new ScreenshotCallback() {
            @Override
            public void onScreenshotTaken(Bitmap screenshot) {
                try {
                    // 保存到文件
                    java.io.FileOutputStream out = new java.io.FileOutputStream(filename);
                    screenshot.compress(Bitmap.CompressFormat.PNG, 100, out);
                    out.flush();
                    out.close();
                    
                    Log.d(TAG, "截图已保存: " + filename);
                    callback.onScreenshotTaken(screenshot);
                    
                } catch (Exception e) {
                    Log.e(TAG, "保存截图失败", e);
                    callback.onError("保存失败: " + e.getMessage());
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 截取屏幕指定区域
     */
    public void captureRegion(int x, int y, int width, int height, ScreenshotCallback callback) {
        captureScreen(new ScreenshotCallback() {
            @Override
            public void onScreenshotTaken(Bitmap screenshot) {
                try {
                    // 确保坐标在有效范围内
                    int cropX = Math.max(0, Math.min(x, screenshot.getWidth() - 1));
                    int cropY = Math.max(0, Math.min(y, screenshot.getHeight() - 1));
                    int cropWidth = Math.min(width, screenshot.getWidth() - cropX);
                    int cropHeight = Math.min(height, screenshot.getHeight() - cropY);
                    
                    Bitmap croppedBitmap = Bitmap.createBitmap(
                        screenshot, cropX, cropY, cropWidth, cropHeight);
                    
                    callback.onScreenshotTaken(croppedBitmap);
                    
                } catch (Exception e) {
                    Log.e(TAG, "裁剪截图失败", e);
                    callback.onError("裁剪失败: " + e.getMessage());
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 获取屏幕尺寸
     */
    public int[] getScreenSize() {
        return new int[]{screenWidth, screenHeight};
    }
    
    /**
     * 检查是否有截图权限
     */
    public boolean hasPermission() {
        return mediaProjection != null;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopContinuousCapture();
        
        if (virtualDisplay != null) {
            virtualDisplay.release();
            virtualDisplay = null;
        }
        
        if (imageReader != null) {
            imageReader.close();
            imageReader = null;
        }
        
        if (mediaProjection != null) {
            mediaProjection.stop();
            mediaProjection = null;
        }
        
        Log.d(TAG, "屏幕截图服务已释放");
    }
    
    /**
     * 设置截图质量
     */
    public void setQuality(int quality) {
        // 可以调整截图的压缩质量
        // quality: 0-100
    }
    
    /**
     * 设置截图格式
     */
    public void setFormat(Bitmap.CompressFormat format) {
        // 可以设置截图保存格式：PNG, JPEG等
    }
    
    /**
     * 获取截图状态
     */
    public boolean isCapturing() {
        return isCapturing;
    }
    
    /**
     * 批量截图
     */
    public void captureBatch(int count, int intervalMs, BatchCallback callback) {
        java.util.List<Bitmap> screenshots = new java.util.ArrayList<>();
        
        Handler batchHandler = new Handler(Looper.getMainLooper());
        final int[] currentCount = {0};
        
        Runnable batchRunnable = new Runnable() {
            @Override
            public void run() {
                if (currentCount[0] >= count) {
                    callback.onBatchCompleted(screenshots);
                    return;
                }
                
                captureScreen(new ScreenshotCallback() {
                    @Override
                    public void onScreenshotTaken(Bitmap screenshot) {
                        screenshots.add(screenshot);
                        currentCount[0]++;
                        
                        callback.onProgress(currentCount[0], count);
                        
                        if (currentCount[0] < count) {
                            batchHandler.postDelayed(this, intervalMs);
                        } else {
                            callback.onBatchCompleted(screenshots);
                        }
                    }
                    
                    @Override
                    public void onError(String error) {
                        callback.onError(error);
                    }
                });
            }
        };
        
        batchHandler.post(batchRunnable);
    }
    
    /**
     * 批量截图回调接口
     */
    public interface BatchCallback {
        void onProgress(int current, int total);
        void onBatchCompleted(java.util.List<Bitmap> screenshots);
        void onError(String error);
    }
}

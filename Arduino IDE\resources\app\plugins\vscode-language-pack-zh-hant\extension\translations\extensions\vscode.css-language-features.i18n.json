{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"\"store\" a boolean test for later evaluation in a guard or if().": "\"store\" 一個布林值測試，以供稍後在 guard 或 if() 中評估。", "'from' expected": "必須是 'from'。", "'in' expected": "預期為 'in'", "'through' or 'to' expected": "必須是 'through' 或 'to'", "'{0}'": "'{0}'", "( expected": "預期為 (", ") expected": "預期為 )", "<undefined>": "<未定義>", "@font-face": "@font-face", "@font-face rule must define 'src' and 'font-family' properties": "@font-face 規則必須定義 'src' 和 'font-family' 屬性", "@keyframes {0}": "@keyframes {0}", "A list of properties that are not validated against the `unknownProperties` rule.": "未對 `unknownProperties` 規則驗證的屬性清單。", "Adds quotes to a string.": "新增引號到字串。", "Also define the standard property '{0}' for compatibility": "也定義標準屬性 '{0}' 以獲得相容性", "Always define standard rule '@keyframes' when defining keyframes.": "定義主要 keyframes 時，一律定義標準規則 '@keyframes'。", "Always include all vendor specific properties: Missing: {0}": "一律包含所有廠商特定屬性: 遺失: {0}", "Always include all vendor specific rules: Missing: {0}": "一律包含所有廠商特定規則: 遺失: {0}", "Appends a single value onto the end of a list.": "將單一值附加到清單結尾。", "Appends selectors to one another without spaces in between.": "將選取器附加至彼此，但兩者之間沒有空格。", "Avoid using !important. It is an indication that the specificity of the entire CSS has gotten out of control and needs to be refactored.": "避免使用 !important。這表示整個 CSS 的明確性皆失控，需要重構。", "Avoid using 'float'. Floats lead to fragile CSS that is easy to break if one aspect of the layout changes.": "避免使用 'float'。浮動會使 CSS 脆弱，在版面配置的任一層面改變時容易中斷。", "CSS Language Server": "CSS 語言伺服器", "CSS fix is outdated and can't be applied to the document.": "CSS 修正已過期，無法套用至文件。", "Causes one or more rules to be emitted at the root of the document.": "導致在文件根目錄發出一或多個規則。", "Changes one or more properties of a color.": "變更色彩的一或多個屬性。", "Changes the alpha component for a color.": "變更色彩的 Alpha 元件。", "Changes the hue of a color.": "變更色彩的色調。", "Combines several lists into a single multidimensional list.": "將數個清單合併成單一多維度清單。", "Converts a color into the format understood by IE filters.": "將色彩轉換成 IE 篩選能理解的格式。", "Converts a color to grayscale.": "將色彩轉換為灰階。", "Converts a string to lower case.": "將字串轉換為小寫。", "Converts a string to upper case.": "將字串轉換為大寫。", "Converts a unitless number to a percentage.": "將無單位數字轉換成百分比。", "Creates a Color from hue, saturation, and lightness values.": "從色調、飽和度和亮度值建立色彩。", "Creates a Color from hue, saturation, lightness, and alpha values.": "從色調、飽和度、亮度和 Alpha 值建立色彩。", "Creates a Color from hue, white, and black values.": "從色調、白色和黑色值建立色彩。", "Creates a Color from lightness, a, and b values.": "根據亮度、a 和 b 值建立色彩。", "Creates a Color from lightness, chroma, and hue values.": "根據亮度、色度和色調值建立色彩。", "Creates a Color from red, green, and blue values.": "從紅色、綠色和藍色值建立色彩。", "Creates a Color from red, green, blue, and alpha values.": "從紅色、綠色、藍色和 Alpha 值建立色彩。", "Creates a Color from the hue, saturation, and lightness values of another Color.": "根據其他色彩的色調、彩度和亮度值建立色彩。", "Creates a Color from the hue, white, and black values of another Color.": "根據其他色彩的色調、白色和黑色值建立色彩。", "Creates a Color from the lightness, a, and b values of another Color.": "根據其他色彩的亮度、a 和 b 值建立色彩。", "Creates a Color from the lightness, chroma, and hue values of another Color.": "根據其他色彩的亮度、色度和色調值建立色彩。", "Creates a Color from the red, green, and blue values of another Color.": "根據其他色彩的紅色、綠色和藍色值建立色彩。", "Creates a Color in a specific color space from red, green, and blue values.": "根據紅色、綠色和藍色值在特定色彩空間中建立色彩。", "Creates a Color in a specific color space from the red, green, and blue values of another Color.": "根據其他色彩的紅色、綠色和藍色值，在特定的色彩空間中建立色彩。", "Defines complex operations that can be re-used throughout stylesheets.": "定義可在樣式表中重複使用的複雜作業。", "Defines styles that can be re-used throughout the stylesheet with `@include`.": "定義可以使用 '@include' 在整個樣式表中重複使用的樣式。", "Do not use duplicate style definitions": "請勿使用重複的樣式定義", "Do not use empty rulesets": "請勿使用空白規則集", "Do not use width or height when using padding or border": "使用 padding 或 border 時不要使用 width 或 height。", "Dynamically calls a Sass function.": "動態呼叫 Sass 函數。", "Each loop that sets `$var` to each item in the list or map, then outputs the styles it contains using that value of `$var`.": "將 '$var' 設為清單或對應中每個項目的每個迴圈，然後使用 '$var' 值輸出其中包含的樣式。", "Exposes the details of Sass’s inner workings.": "公開 Sass 內部工作的詳細資料。", "Extends $extendee with $extender within $selector.": "使用 $selector 內的 $extender 擴充 $extendee。", "Extracts a substring from $string.": "從 $string 擷取子字串。", "Failed to apply CSS fix to the document. Please consider opening an issue with steps to reproduce.": "無法將 CSS 修正套用到文件。請考慮開啟具有重現步驟的問題。", "Finds the maximum of several numbers.": "尋找數個數字的最大值。", "Finds the minimum of several numbers.": "尋找數個數字的最小值。", "Fluidly scales one or more properties of a color.": "流暢地縮放色彩的一或多個屬性。", "Folding Region End": "摺疊區域結束", "Folding Region Start": "摺疊區域開始", "For loop that repeatedly outputs a set of styles for each `$var` in the `from/through` or `from/to` clause.": "針對會為 `from/through` 或 `from/to` 子句中的每個 '$var' 重複輸出一組樣式的迴圈。", "Generates new colors based on existing ones, making it easy to build color themes.": "根據現有的色彩產生新色彩，輕鬆建置色彩主題。", "Gets the blue component of a color.": "取得色彩的藍色元件。", "Gets the green component of a color.": "取得色彩的綠色元件。", "Gets the hue component of a color.": "取得色彩的色調元件。", "Gets the lightness component of a color.": "取得色彩的亮度元件。", "Gets the opacity component of a color.": "取得色彩的不透明度元件。", "Gets the red component of a color.": "取得色彩的紅色元件。", "Gets the saturation component of a color.": "取得色彩的彩度元件。", "Hex colors must consist of three, four, six or eight hex numbers": "十六進位色彩必須由三、四、六或八個十六進位數字組成", "IE hacks are only necessary when supporting IE7 and older": "只有在支援 IE7 及更舊的版本時才需要 IE Hack", "Import statements do not load in parallel": "匯入陳述式不會平行載入", "Includes the body if the expression does not evaluate to `false` or `null`.": "如果運算式的計算結果不是 'false' 或 'null'，則包含主體。", "Includes the styles defined by another mixin into the current rule.": "將另一個 Mixen 定義的樣式包含至目前的規則中。", "Increases or decreases one or more components of a color.": "增加或減少色彩的一或多個元件。", "Inherits the styles of another selector.": "繼承另一個選取器的樣式。", "Insert url() Function": "插入 url() 函數", "Insert url() Functions": "插入 url() 函數", "Inserts $insert into $string at $index.": "將 $insert 於 $index 插入 $string。", "Invalid number of parameters": "無效的參數數目", "Joins together two lists into one.": "將兩個清單結合成一個清單。", "Lets you access and modify values in lists.": "可讓您存取及修改清單中的值。", "Loads a Sass stylesheet and makes its mixins, functions, and variables available when this stylesheet is loaded with the @use rule.": "載入 Sass 樣式表，當此樣式表載入 @use 規則時，使用其 mixin、函數和變數。", "Loads mixins, functions, and variables from other Sass stylesheets as 'modules', and combines CSS from multiple stylesheets together.": "將來自其他 Sass 樣式表的 Mixin、函式和變數載入為「模組」，並將來自多個樣式表的 CSS 結合在一起。", "Makes a color darker.": "讓色彩更深。", "Makes a color less saturated.": "讓色彩較不飽和。", "Makes a color lighter.": "讓色彩更淺。", "Makes a color more opaque.": "讓色彩更不透明。", "Makes a color more saturated.": "讓色彩更飽和。", "Makes a color more transparent.": "讓色彩更透明。", "Makes it easy to combine, search, or split apart strings.": "輕鬆合併、搜尋或分割字串。", "Makes it possible to look up the value associated with a key in a map, and much more.": "可讓您查詢與對應中索引鍵相關聯的值，還有更多功能。", "Merges two maps together into a new map.": "將兩張地圖合併成一張新地圖。", "Mix two colors together in a polar color space.": "在兩極色彩空間中將兩種色彩混合在一起。", "Mix two colors together in a rectangular color space.": "在矩形色彩空間中將兩種色彩混合在一起。", "Mixes two colors together.": "將兩種色彩混合在一起。", "Nests selector beneath one another like they would be nested in the stylesheet.": "將選取器巢狀於彼此之下，就像在樣式表建立巢狀。", "No unit for zero needed": "零不需要任何單位", "Parses a selector into the format returned by &.": "將選取器剖析為 & 所傳回的格式。", "Prints the value of an expression to the standard error output stream. Useful for debugging complicated Sass files.": "將運算式的值列印到標準錯誤輸出資料流。適用於偵錯複雜的 Sass 檔案。", "Prints the value of an expression to the standard error output stream. Useful for libraries that need to warn users of deprecations or recovering from minor mixin usage mistakes. Warnings can be turned off with the `--quiet` command-line option or the `:quiet` Sass option.": "將運算式的值輸出到標準錯誤輸出資料流。適用於需要警告使用者淘汰或從次要 mixin 使用錯誤復原的程式庫。可以使用 '--quiet' 命令列選項或 `:quiet` Sass 選項關閉警告。", "Property is ignored due to the display.": "由於顯示的原因已略過屬性。", "Property is ignored due to the display. With 'display: block', vertical-align should not be used.": "由於顯示，已略過屬性。針對 'display: block'，不應使用垂直對齊。", "Provides access to Sass’s powerful selector engine.": "提供存取 Sass 強大的選取器引擎。", "Provides functions that operate on numbers.": "提供對數字運算的函式。", "Removes quotes from a string.": "移除字串中的引號。", "Rename to '{0}'": "重新命名為 '{0}'", "Replaces $original with $replacement within $selector.": "以 $selector 内的 $replacement 取代 $original。", "Replaces the nth item in a list.": "取代清單中的第 n 個項目。", "Returns a list of all keys in a map.": "傳回對應中所有索引鍵的清單。", "Returns a list of all values in a map.": "傳回對應中所有值的清單。", "Returns a new map with keys removed.": "傳回新對應，其中將索引鍵移除。", "Returns a random number.": "傳回亂數。", "Returns a specific item in a list.": "傳回清單中的特定項目。", "Returns the absolute value of a number.": "傳回數字的絕對值。", "Returns the complement of a color.": "傳回色彩的補數。", "Returns the index of the first occurance of $substring in $string.": "傳回 $string 中 $substring 的第一次出現項目的索引。", "Returns the inverse of a color.": "傳回色彩的反函數。", "Returns the keywords passed to a function that takes variable arguments.": "傳回傳遞至採用變數引數函式的關鍵字。", "Returns the length of a list.": "傳回清單的長度。", "Returns the number of characters in a string.": "傳回字串中的字元數", "Returns the position of a value within a list.": "傳回值在清單中的位置。", "Returns the separator of a list.": "傳回清單的分隔符號。", "Returns the simple selectors that comprise a compound selector.": "傳回組成複合選取器的簡單選取器。", "Returns the string representation of a value as it would be represented in Sass.": "傳回值在 Sass 中表示的字串。", "Returns the type of a value.": "傳回值的類型。", "Returns the unit(s) associated with a number.": "傳回與數字相關聯的單位。", "Returns the value in a map associated with a given key.": "傳回與指定索引碼相關之對應中的值。", "Returns whether $super matches all the elements $sub does, and possibly more.": "傳回 $super 是否與 $sub 符合的所有元素相符，以及其符合項目是否更多。", "Returns whether a feature exists in the current Sass runtime.": "傳回功能是否存在於目前的 Sass 執行階段中。", "Returns whether a function with the given name exists.": "傳回具有指定名稱的函數是否存在。", "Returns whether a map has a value associated with a given key.": "傳回對應是否有與指定索引鍵相關的值。", "Returns whether a mixin with the given name exists.": "傳回具有指定名稱的 Mixin 是否存在。", "Returns whether a number has units.": "傳回數字是否有單位。", "Returns whether a variable with the given name exists in the current scope.": "傳回具有指定名稱的變數是否存在於目前範圍中。", "Returns whether a variable with the given name exists in the global scope.": "傳回具有指定名稱的變數是否存在於全域範圍。", "Returns whether two numbers can be added, subtracted, or compared.": "傳回兩個數字是否可相加、相減或比較。", "Rounds a number down to the previous whole number.": "將數字無條件捨去到前一個整數。", "Rounds a number to the nearest whole number.": "將數字捨去到最接近的整數。", "Rounds a number up to the next whole number.": "將數字無條件進位入到下一個整數。", "Sass documentation": "Sass 文件", "Selector Specificity": "選取器明確性", "Selectors should not contain IDs because these rules are too tightly coupled with the HTML.": "選取器不應包含 ID，因為這些規則與 HTML 結合過於緊密。", "The universal selector (*) is known to be slow": "已知通用選取器 (*) 速度緩慢", "Throws the value of an expression as a fatal error with stack trace. Useful for validating arguments to mixins and functions.": "擲出運算式的值，作為堆疊追蹤的嚴重錯誤。對於驗證 Mixin 和函數的引數很有用。", "URI expected": "預期為 URI", "URL encodes a string": "URL 編碼字串", "Unifies two selectors to produce a selector that matches elements matched by both.": "統一兩個選取器，以產生與兩者相符的元素符合的選取器。", "Unknown at-rule.": "未知的 at-rule。", "Unknown property.": "未知的屬性。", "Unknown property: '{0}'": "未知的屬性: '{0}'", "Unknown vendor specific property.": "未知的廠商特定屬性。", "When using a vendor-specific prefix also include the standard property": "在使用廠商專屬的前置詞時，也包括標準屬性", "When using a vendor-specific prefix make sure to also include all other vendor-specific properties": "在使用廠商專屬的前置詞時，請確定也包括其他所有的廠商專屬屬性", "While loop that takes an expression and repeatedly outputs the nested styles until the statement evaluates to `false`.": "While 迴圈會採用運算式並重複輸出巢狀樣式，直到陳述式評估為 `false` 為止。", "[ expected": "預期為 [", "] expected": "必須是 ]", "absolute value of a number": "數字的絕對值", "arccosine - inverse of cosine function": "反餘弦 - 餘弦的反向", "arcsine - inverse of sine function": "反正弦 - 正弦函式的反向", "arctangent - inverse of tangent function": "反正切 - 正切函式的反向", "argument from '{0}'": "來自 '{0}' 的引數", "at-rule or selector expected": "預期為 at-rule 或選取器", "at-rule unknown": "未知的 at-rule", "bind the evaluation of a ruleset to each member of a list.": "將規則集的評估與清單的每個成員繫結。", "calculates square root of a number": "計算數字的平方根", "colon expected": "必須是冒號", "comma expected": "必須是逗號", "condition expected": "必須是條件", "converts numbers from one type into another": "將數字從一種類型轉換成另一種類型", "converts to a %, e.g. 0.5 > 50%": "轉換成 %，例如 0.5 > 50%", "cosine function": "餘弦函式", "creates a #AARRGGBB": "建立 #AARRGGBB", "creates a color": "建立色彩", "dot expected": "必須是點", "escape string content": "逸出字串內容", "expression expected": "必須是運算式", "first argument modulus second argument": "第一個引數對第二個引數取用模數", "first argument raised to the power of the second argument": "第一個引數的第二個引數次方", "generate a list spanning a range of values": "產生跨越值範圍的清單", "identifier expected": "必須是識別碼", "identifier or variable expected": "預期為識別碼或變數", "identifier or wildcard expected": "必須是識別碼或萬用字元", "inline-block is ignored due to the float. If 'float' has a value other than 'none', the box is floated and 'display' is treated as 'block'": "由於 float，因此略過 inline-block。如果 'float' 的值不是 'none'，則會浮動該方塊，且將 'display' 視為 'block'", "inlines a resource and falls back to `url()`": "將資源內嵌，並回復為 'url()'", "media query expected": "預期為媒體查詢", "number expected": "必須是數字", "operator expected": "必須是運算子", "page directive or declaraton expected": "預期為頁面指示詞或宣告", "parses a string to a color": "將字串剖析為色彩", "percentage expected": "預期為百分比", "property value expected": "預期為屬性值", "remove or change the unit of a dimension": "移除或變更維度的單位", "return `@color` 10% points darker": "傳回 '@color' 較深 10% 點", "return `@color` 10% points less saturated": "傳回 '@color' 飽和度低 10%", "return `@color` 10% points less transparent": "傳回透明度低 10% 的 `@color`", "return `@color` 10% points lighter": "傳回 '@color' 較淺 10% 點", "return `@color` 10% points more saturated": "傳回 '@color' 飽和度高 10%", "return `@color` 10% points more transparent": "傳回 '@color' 透明度高 10%", "return `@color` with 50% transparency": "傳回具有 50% 透明度的 `@color`", "return `@color` with a 10 degree larger in hue": "傳回具有的色調大 10 度的 `@color`", "return `@darkcolor` if `@color1 is> 43% luma` otherwise return `@lightcolor`, see notes": "若 `@color1 is> 43% luma` 會傳回 `@darkcolor`，否則是傳回 `@lightcolor`，請查看記事", "return a mix of `@color1` and `@color2`": "傳回 `@color1` 和 `@color2` 的混合", "returns a grey, 100% desaturated color": "傳回灰色，100% 還原飽和色彩", "returns a value at the specified position in the list": "傳回清單中指定位置的值", "returns one of two values depending on a condition.": "根據條件傳回兩個值的其中一個。", "returns pi": "returns pi", "returns the `alpha` channel of `@color`": "傳回 `@color` 的 `alpha` 通道", "returns the `blue` channel of `@color`": "傳回 `@color` 的 `blue` 通道", "returns the `green` channel of `@color`": "傳回 `@color` 的 `green` 通道", "returns the `hue` channel of `@color` in the HSL space": "傳回 HSL 空間中 '@color' 的 'hue' 通道", "returns the `hue` channel of `@color` in the HSV space": "傳回 HSV 空間中 '@color' 的 'hue' 通道", "returns the `lightness` channel of `@color` in the HSL space": "傳回 HSL 空間中 `@color` 的 `lightness` 通道", "returns the `luma` value (perceptual brightness) of `@color`": "傳回 '@color' 的 'luma' 值 (感知亮度)", "returns the `red` channel of `@color`": "傳回 `@color` 的 `red` 通道", "returns the `saturation` channel of `@color` in the HSL space": "傳回 HSL 空間中 '@color' 的 `saturation` 通道", "returns the `saturation` channel of `@color` in the HSV space": "傳回 HSV 空間中 `@color` 的 `saturation` 通道", "returns the `value` channel of `@color` in the HSV space": "傳回 HSV 空間中 '@color' 的 `value` 通道", "returns the lowest of one or more values": "傳回一或多個值的最低值", "returns the number of elements in a value list": "傳回值清單中的元素數目", "rounds a number to a number of places": "將數字捨入到多個位置", "rounds down to an integer": "無條件捨去至整數", "rounds up to an integer": "無條件進入至整數", "selector expected": "必須是選取器", "semi-colon expected": "必須是冒號", "sine function": "正弦函數", "string literal expected": "必須是字串常值", "string replace": "字串取代", "tangent function": "正切函數", "term expected": "必須是字詞", "unknown keyword": "未知的關鍵字", "uri or string expected": "預期為 URI 或字串", "variable name expected": "必須是變數名稱", "variable value expected": "必須是變數值", "whitespace expected": "必須是空白字元", "wildcard expected": "預期為萬用字元", "{ expected": "必須是 {", "{0}, '{1}'": "{0}、'{1}'", "} expected": "必須是 }"}, "package": {"css.colorDecorators.enable.deprecationMessage": "設定 `css.colorDecorators.enable` 已淘汰，改為 `editor.colorDecorators`。", "css.completion.completePropertyWithSemicolon.desc": "完成 CSS 屬性時在行尾插入分號。", "css.completion.triggerPropertyValueCompletion.desc": "根據預設，VS Code 會在選取 CSS 屬性後，觸發屬性值完成。請使用此設定停用此行為。", "css.customData.desc": "指向 JSON 檔案的相對檔案路徑清單，採用[自訂資料格式](https://github.com/microsoft/vscode-css-languageservice/blob/master/docs/customData.md)。\r\n\r\nVS Code 會在啟動時載入自訂資料，針對您在 JSON 檔案中所指定的 CSS 自訂屬性 (變數)、at 規則、虛擬類別以及虛擬元素，增強其 CSS 支援。\r\n\r\n檔案路徑相對於工作區，而且僅會考慮工作區資料夾設定。", "css.format.braceStyle.desc": "將大括弧放在與規則 ('collapse') 的同一行，或將大括弧放在自己的行上 (`expand`)。", "css.format.enable.desc": "啟用/停用預設 CSS 格式器。", "css.format.maxPreserveNewLines.desc": "啟用 '#css.format.preserveNewLines#' 時，要保留在一個區塊中的分行符號數目上限。", "css.format.newlineBetweenRules.desc": "以空白行分隔規則集。", "css.format.newlineBetweenSelectors.desc": "以新行分隔選取器。", "css.format.preserveNewLines.desc": "是否應該保留元素之前的現有分行符號。", "css.format.spaceAroundSelectorSeparator.desc": "請確認選擇器分隔字元 '>'、'+'、'~' 周圍有空白字元 (例如 `a > b`)。", "css.hover.documentation": "顯示 CSS 懸停中的屬性和值文件。", "css.hover.references": "在 CSS 暫留時顯示 MDN 參考。", "css.lint.argumentsInColorFunction.desc": "錯誤的參數個數。", "css.lint.boxModel.desc": "使用 'padding' 或 'border' 時不要使用 'width' 或 'height'。", "css.lint.compatibleVendorPrefixes.desc": "在使用廠商專屬的前置詞時，請確定也包括其他所有的廠商專屬屬性。 ", "css.lint.duplicateProperties.desc": "請勿使用重複的樣式定義。 ", "css.lint.emptyRules.desc": "請勿使用空白規則集。", "css.lint.float.desc": "避免使用 'float'。浮動會使 CSS 脆弱，在版面配置的任一層面改變時容易中斷。", "css.lint.fontFaceProperties.desc": "`@font-face` 規則必須定義 `src` 和 `font-family` 屬性。", "css.lint.hexColorLength.desc": "十六進位色彩必須由 3、4、6 或 8 個十六進位數字組成。", "css.lint.idSelector.desc": "選取器不應包含 ID，因為這些規則與 HTML 結合過於緊密。", "css.lint.ieHack.desc": "只有在支援 IE7 及更舊的版本時才需要 IE Hack。", "css.lint.importStatement.desc": "匯入陳述式不會平行載入。", "css.lint.important.desc": "避免使用 '!important'。這表示整個 CSS 的明確性皆失控，需要重構。", "css.lint.propertyIgnoredDueToDisplay.desc": "屬性因為顯示的關係而略過。例如，'display: inline' 會讓 'width'、'height'、'margin-top'、'margin-bottom' 及 'float' 屬性無效。", "css.lint.universalSelector.desc": "已知通用選取器 (`*`) 速度緩慢。", "css.lint.unknownAtRules.desc": "未知的 at-rule。", "css.lint.unknownProperties.desc": "未知的屬性。", "css.lint.unknownVendorSpecificProperties.desc": "未知的廠商特定屬性。", "css.lint.validProperties.desc": "未對 `unknownProperties` 規則驗證的屬性清單。", "css.lint.vendorPrefix.desc": "在使用廠商專屬的前置詞時，也包括標準屬性。", "css.lint.zeroUnits.desc": "零不需要任何單位。", "css.title": "CSS", "css.trace.server.desc": "追蹤 VS Code 與 CSS 語言伺服器之間的通訊。", "css.validate.desc": "啟用或停用所有驗證。", "css.validate.title": "控制 CSS 驗證與問題嚴重性。", "description": "為 CSS, LESS 和 SCSS 檔案提供豐富的語言支援 ", "displayName": "CSS 語言功能", "less.colorDecorators.enable.deprecationMessage": "設定 `less.colorDecorators.enable` 已淘汰，改為 `editor.colorDecorators`。", "less.completion.completePropertyWithSemicolon.desc": "完成 CSS 屬性時在行尾插入分號。", "less.completion.triggerPropertyValueCompletion.desc": "根據預設，VS Code 會在選取 CSS 屬性後，觸發屬性值完成。請使用此設定停用此行為。", "less.format.braceStyle.desc": "將大括弧放在與規則 ('collapse') 的同一行，或將大括弧放在自己的行上 (`expand`)。", "less.format.enable.desc": "啟用/停用預設 LESS 格式器。", "less.format.maxPreserveNewLines.desc": "啟用 '#less.format.preserveNewLines#' 時，要保留在一個區塊中的分行符號數目上限。", "less.format.newlineBetweenRules.desc": "以空白行分隔規則集。", "less.format.newlineBetweenSelectors.desc": "以新行分隔選取器。", "less.format.preserveNewLines.desc": "是否應該保留元素之前的現有分行符號。", "less.format.spaceAroundSelectorSeparator.desc": "請確認選擇器分隔字元 '>'、'+'、'~' 周圍有空白字元 (例如 `a > b`)。", "less.hover.documentation": "顯示 LESS 懸停中的屬性和值文件。", "less.hover.references": "在 LESS 暫留時顯示 MDN 參考。", "less.lint.argumentsInColorFunction.desc": "錯誤的參數個數。", "less.lint.boxModel.desc": "使用 'padding' 或 'border' 時不要使用 'width' 或 'height'。", "less.lint.compatibleVendorPrefixes.desc": "在使用廠商專屬的前置詞時，請確定也包括其他所有的廠商專屬屬性。 ", "less.lint.duplicateProperties.desc": "請勿使用重複的樣式定義。 ", "less.lint.emptyRules.desc": "請勿使用空白規則集。", "less.lint.float.desc": "避免使用 'float'。浮動會使 CSS 脆弱，在版面配置的任一層面改變時容易中斷。", "less.lint.fontFaceProperties.desc": "`@font-face` 規則必須定義 `src` 和 `font-family` 屬性。", "less.lint.hexColorLength.desc": "十六進位色彩必須由 3、4、6 或 8 個十六進位數字組成。", "less.lint.idSelector.desc": "選取器不應包含 ID，因為這些規則與 HTML 結合過於緊密。", "less.lint.ieHack.desc": "只有在支援 IE7 及更舊的版本時才需要 IE Hack。", "less.lint.importStatement.desc": "匯入陳述式不會平行載入。", "less.lint.important.desc": "避免使用 '!important'。這表示整個 CSS 的明確性皆失控，需要重構。", "less.lint.propertyIgnoredDueToDisplay.desc": "屬性因為顯示的關係而略過。例如，'display: inline' 會讓 'width'、'height'、'margin-top'、'margin-bottom' 及 'float' 屬性無效。", "less.lint.universalSelector.desc": "已知通用選取器 (`*`) 速度緩慢。", "less.lint.unknownAtRules.desc": "未知的 at-rule。", "less.lint.unknownProperties.desc": "未知的屬性。", "less.lint.unknownVendorSpecificProperties.desc": "未知的廠商特定屬性。", "less.lint.validProperties.desc": "未對 `unknownProperties` 規則驗證的屬性清單。", "less.lint.vendorPrefix.desc": "在使用廠商專屬的前置詞時，也包括標準屬性。", "less.lint.zeroUnits.desc": "零不需要任何單位。", "less.title": "LESS", "less.validate.desc": "啟用或停用所有驗證。", "less.validate.title": "控制 LESS 驗證與問題嚴重性。", "scss.colorDecorators.enable.deprecationMessage": "設定 `scss.colorDecorators.enable` 已淘汰，改為 `editor.colorDecorators`。", "scss.completion.completePropertyWithSemicolon.desc": "完成 CSS 屬性時在行尾插入分號。", "scss.completion.triggerPropertyValueCompletion.desc": "根據預設，VS Code 會在選取 CSS 屬性後，觸發屬性值完成。請使用此設定停用此行為。", "scss.format.braceStyle.desc": "將大括弧放在與規則 ('collapse') 的同一行，或將大括弧放在自己的行上 (`expand`)。", "scss.format.enable.desc": "啟用/停用預設 SCSS 格式器。", "scss.format.maxPreserveNewLines.desc": "啟用 '#scss.format.preserveNewLines#' 時，要保留在一個區塊中的分行符號數目上限。", "scss.format.newlineBetweenRules.desc": "以空白行分隔規則集。", "scss.format.newlineBetweenSelectors.desc": "以新行分隔選取器。", "scss.format.preserveNewLines.desc": "是否應該保留元素之前的現有分行符號。", "scss.format.spaceAroundSelectorSeparator.desc": "請確認選擇器分隔字元 '>'、'+'、'~' 周圍有空白字元 (例如 `a > b`)。", "scss.hover.documentation": "顯示 SCSS 懸停中的屬性和值文件。", "scss.hover.references": "在 SCSS 暫留時顯示 MDN 參考。", "scss.lint.argumentsInColorFunction.desc": "錯誤的參數個數。", "scss.lint.boxModel.desc": "使用 'padding' 或 'border' 時不要使用 'width' 或 'height'。", "scss.lint.compatibleVendorPrefixes.desc": "在使用廠商專屬的前置詞時，請確定也包括其他所有的廠商專屬屬性。 ", "scss.lint.duplicateProperties.desc": "請勿使用重複的樣式定義。 ", "scss.lint.emptyRules.desc": "請勿使用空白規則集。", "scss.lint.float.desc": "避免使用 'float'。浮動會使 CSS 脆弱，在版面配置的任一層面改變時容易中斷。", "scss.lint.fontFaceProperties.desc": "`@font-face` 規則必須定義 `src` 和 `font-family` 屬性。", "scss.lint.hexColorLength.desc": "十六進位色彩必須由 3、4、6 或 8 個十六進位數字組成。", "scss.lint.idSelector.desc": "選取器不應包含 ID，因為這些規則與 HTML 結合過於緊密。", "scss.lint.ieHack.desc": "只有在支援 IE7 及更舊的版本時才需要 IE Hack。", "scss.lint.importStatement.desc": "匯入陳述式不會平行載入。", "scss.lint.important.desc": "避免使用 '!important'。這表示整個 CSS 的明確性皆失控，需要重構。", "scss.lint.propertyIgnoredDueToDisplay.desc": "屬性因為顯示的關係而略過。例如，'display: inline' 會讓 'width'、'height'、'margin-top'、'margin-bottom' 及 'float' 屬性無效。", "scss.lint.universalSelector.desc": "已知通用選取器 (`*`) 速度緩慢。", "scss.lint.unknownAtRules.desc": "未知的 at-rule。", "scss.lint.unknownProperties.desc": "未知的屬性。", "scss.lint.unknownVendorSpecificProperties.desc": "未知的廠商特定屬性。", "scss.lint.validProperties.desc": "未對 `unknownProperties` 規則驗證的屬性清單。", "scss.lint.vendorPrefix.desc": "在使用廠商專屬的前置詞時，也包括標準屬性。", "scss.lint.zeroUnits.desc": "零不需要任何單位。", "scss.title": "SCSS (Sass)", "scss.validate.desc": "啟用或停用所有驗證。", "scss.validate.title": "控制 SCSS 驗證與問題嚴重性。"}}}
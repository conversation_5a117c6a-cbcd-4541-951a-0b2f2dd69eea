{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Auto detecting gulp for folder {0} failed with error: {1}', this.workspaceFolder.name, err.error ? err.error.toString() : 'unknown": "自動偵測資料夾 {0} 的 gulp 失敗，錯誤為: {1}'， this.workspaceFolder.name, err.error ? err.error.toString() : 'unknown", "Go to output": "前往輸出", "Problem finding gulp tasks. See the output for more information.": "尋找 Gulp 工作時發生問題。如需詳細資訊，請參閱輸出。"}, "package": {"config.gulp.autoDetect": "控制 Gulp 工作偵測的啟用。Gulp 工作偵測可能會導致在任何開啟的工作區中執行檔案。", "description": "將 Gulp 功能新增至 VSCode 的延伸模組。", "displayName": "VSCode 的 Gulp 支援。", "gulp.taskDefinition.file.description": "提供工作的 Gulp 檔案。可以省略。", "gulp.taskDefinition.type.description": "要自訂的 Gulp 工作。"}}}
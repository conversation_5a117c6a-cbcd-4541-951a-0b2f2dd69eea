package com.bluetooth.automation;

import android.accessibilityservice.AccessibilityService;
import android.content.Intent;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;

import java.util.List;

/**
 * 无障碍服务
 * 用于执行自动化操作和监听界面变化
 */
public class AutomationAccessibilityService extends AccessibilityService {
    
    private static final String TAG = "AutomationAccessibility";
    private static AutomationAccessibilityService instance;
    
    // 目标应用包名
    private static final String[] TARGET_PACKAGES = {
        "com.ss.android.ugc.aweme.lite",  // 抖音极速版
        "com.kuaishou.nebula",            // 快手极速版
        "com.tencent.mm",                 // 微信
        "com.taobao.taobao",             // 淘宝
        "com.jingdong.app.mall"          // 京东
    };
    
    // 广告相关关键词
    private static final String[] AD_KEYWORDS = {
        "跳过", "skip", "关闭", "close", "×", "✕",
        "广告", "ad", "推广", "sponsor",
        "观看视频", "watch", "获得奖励", "reward"
    };
    
    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        if (event == null) {
            return;
        }
        
        String packageName = event.getPackageName() != null ? 
            event.getPackageName().toString() : "";
        
        // 只处理目标应用的事件
        if (!isTargetPackage(packageName)) {
            return;
        }
        
        int eventType = event.getEventType();
        
        switch (eventType) {
            case AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED:
                handleWindowStateChanged(event);
                break;
                
            case AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED:
                handleWindowContentChanged(event);
                break;
                
            case AccessibilityEvent.TYPE_VIEW_CLICKED:
                handleViewClicked(event);
                break;
                
            case AccessibilityEvent.TYPE_VIEW_SCROLLED:
                handleViewScrolled(event);
                break;
        }
    }
    
    @Override
    public void onInterrupt() {
        Log.d(TAG, "无障碍服务被中断");
    }
    
    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        instance = this;
        Log.d(TAG, "无障碍服务已连接");
    }
    
    @Override
    public boolean onUnbind(Intent intent) {
        instance = null;
        Log.d(TAG, "无障碍服务已断开");
        return super.onUnbind(intent);
    }
    
    /**
     * 获取服务实例
     */
    public static AutomationAccessibilityService getInstance() {
        return instance;
    }
    
    /**
     * 检查是否为目标应用
     */
    private boolean isTargetPackage(String packageName) {
        for (String targetPackage : TARGET_PACKAGES) {
            if (targetPackage.equals(packageName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理窗口状态变化
     */
    private void handleWindowStateChanged(AccessibilityEvent event) {
        Log.d(TAG, "窗口状态变化: " + event.getClassName());
        
        // 检测是否进入广告页面
        if (isAdActivity(event.getClassName().toString())) {
            Log.d(TAG, "检测到广告页面");
            // 可以在这里触发广告检测逻辑
        }
    }
    
    /**
     * 处理窗口内容变化
     */
    private void handleWindowContentChanged(AccessibilityEvent event) {
        // 检测广告相关元素
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode != null) {
            findAndHandleAdElements(rootNode);
            rootNode.recycle();
        }
    }
    
    /**
     * 处理点击事件
     */
    private void handleViewClicked(AccessibilityEvent event) {
        Log.d(TAG, "检测到点击事件: " + event.getText());
    }
    
    /**
     * 处理滚动事件
     */
    private void handleViewScrolled(AccessibilityEvent event) {
        Log.d(TAG, "检测到滚动事件");
    }
    
    /**
     * 检查是否为广告Activity
     */
    private boolean isAdActivity(String className) {
        String lowerClassName = className.toLowerCase();
        return lowerClassName.contains("ad") || 
               lowerClassName.contains("advertisement") ||
               lowerClassName.contains("promotion");
    }
    
    /**
     * 查找并处理广告元素
     */
    private void findAndHandleAdElements(AccessibilityNodeInfo rootNode) {
        if (rootNode == null) {
            return;
        }
        
        // 查找关闭按钮
        List<AccessibilityNodeInfo> closeButtons = findNodesByText(rootNode, AD_KEYWORDS);
        
        for (AccessibilityNodeInfo node : closeButtons) {
            if (node.isClickable()) {
                Log.d(TAG, "找到可点击的广告元素: " + node.getText());
                // 这里可以记录位置信息，供后续自动点击使用
            }
        }
        
        // 回收节点
        for (AccessibilityNodeInfo node : closeButtons) {
            node.recycle();
        }
    }
    
    /**
     * 根据文本查找节点
     */
    private List<AccessibilityNodeInfo> findNodesByText(AccessibilityNodeInfo rootNode, String[] keywords) {
        List<AccessibilityNodeInfo> result = new java.util.ArrayList<>();
        
        for (String keyword : keywords) {
            List<AccessibilityNodeInfo> nodes = rootNode.findAccessibilityNodeInfosByText(keyword);
            if (nodes != null) {
                result.addAll(nodes);
            }
        }
        
        return result;
    }
    
    /**
     * 查找具有特定ID的节点
     */
    public AccessibilityNodeInfo findNodeById(String id) {
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode == null) {
            return null;
        }
        
        List<AccessibilityNodeInfo> nodes = rootNode.findAccessibilityNodeInfosByViewId(id);
        rootNode.recycle();
        
        return nodes.isEmpty() ? null : nodes.get(0);
    }
    
    /**
     * 查找包含特定文本的节点
     */
    public AccessibilityNodeInfo findNodeByText(String text) {
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode == null) {
            return null;
        }
        
        List<AccessibilityNodeInfo> nodes = rootNode.findAccessibilityNodeInfosByText(text);
        rootNode.recycle();
        
        return nodes.isEmpty() ? null : nodes.get(0);
    }
    
    /**
     * 点击指定节点
     */
    public boolean clickNode(AccessibilityNodeInfo node) {
        if (node == null || !node.isClickable()) {
            return false;
        }
        
        return node.performAction(AccessibilityNodeInfo.ACTION_CLICK);
    }
    
    /**
     * 滚动到指定节点
     */
    public boolean scrollToNode(AccessibilityNodeInfo node) {
        if (node == null) {
            return false;
        }
        
        return node.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD);
    }
    
    /**
     * 获取当前应用包名
     */
    public String getCurrentPackageName() {
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode == null) {
            return null;
        }
        
        String packageName = rootNode.getPackageName() != null ? 
            rootNode.getPackageName().toString() : null;
        
        rootNode.recycle();
        return packageName;
    }
    
    /**
     * 获取当前Activity类名
     */
    public String getCurrentActivityName() {
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode == null) {
            return null;
        }
        
        String className = rootNode.getClassName() != null ? 
            rootNode.getClassName().toString() : null;
        
        rootNode.recycle();
        return className;
    }
    
    /**
     * 检查是否在广告页面
     */
    public boolean isInAdPage() {
        String packageName = getCurrentPackageName();
        String activityName = getCurrentActivityName();
        
        if (packageName == null || activityName == null) {
            return false;
        }
        
        return isTargetPackage(packageName) && isAdActivity(activityName);
    }
    
    /**
     * 自动处理广告
     */
    public boolean autoHandleAd() {
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode == null) {
            return false;
        }
        
        try {
            // 查找关闭按钮
            for (String keyword : AD_KEYWORDS) {
                List<AccessibilityNodeInfo> nodes = rootNode.findAccessibilityNodeInfosByText(keyword);
                
                for (AccessibilityNodeInfo node : nodes) {
                    if (node.isClickable()) {
                        Log.d(TAG, "自动点击广告关闭按钮: " + keyword);
                        boolean success = clickNode(node);
                        
                        // 回收节点
                        for (AccessibilityNodeInfo n : nodes) {
                            n.recycle();
                        }
                        
                        return success;
                    }
                }
                
                // 回收节点
                for (AccessibilityNodeInfo node : nodes) {
                    node.recycle();
                }
            }
            
            return false;
            
        } finally {
            rootNode.recycle();
        }
    }
    
    /**
     * 获取屏幕上所有可点击的节点
     */
    public List<AccessibilityNodeInfo> getAllClickableNodes() {
        List<AccessibilityNodeInfo> clickableNodes = new java.util.ArrayList<>();
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        
        if (rootNode != null) {
            findClickableNodes(rootNode, clickableNodes);
        }
        
        return clickableNodes;
    }
    
    /**
     * 递归查找可点击节点
     */
    private void findClickableNodes(AccessibilityNodeInfo node, List<AccessibilityNodeInfo> result) {
        if (node == null) {
            return;
        }
        
        if (node.isClickable()) {
            result.add(node);
        }
        
        int childCount = node.getChildCount();
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child != null) {
                findClickableNodes(child, result);
                child.recycle();
            }
        }
    }
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试脚本
用于测试各个模块的功能
"""

import asyncio
import logging
import time
import cv2
import numpy as np
from typing import Dict, Any

from bluetooth_controller import BluetoothController
from screen_capture import ScreenCapture
from ad_detector import AdDetector
from task_executor import TaskExecutor
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AutomationTester:
    """自动化测试器"""
    
    def __init__(self):
        self.config = Config()
        self.screen_capture = ScreenCapture()
        self.ad_detector = AdDetector()
        self.task_executor = TaskExecutor()
        self.bluetooth = None
        
    async def test_all_modules(self):
        """测试所有模块"""
        print("=== 开始自动化系统测试 ===\n")
        
        # 测试配置模块
        await self.test_config()
        
        # 测试屏幕捕获
        await self.test_screen_capture()
        
        # 测试广告检测
        await self.test_ad_detection()
        
        # 测试任务执行
        await self.test_task_execution()
        
        # 测试蓝牙连接（可选）
        await self.test_bluetooth_connection()
        
        print("\n=== 测试完成 ===")
    
    async def test_config(self):
        """测试配置模块"""
        print("1. 测试配置模块...")
        
        try:
            # 测试配置读取
            cycle_interval = self.config.cycle_interval
            print(f"   循环间隔: {cycle_interval}秒")
            
            # 测试配置设置
            self.config.set('test.value', 123)
            test_value = self.config.get('test.value')
            assert test_value == 123, "配置设置/读取失败"
            
            # 测试配置验证
            is_valid = self.config.validate_config()
            print(f"   配置验证: {'通过' if is_valid else '失败'}")
            
            print("   ✓ 配置模块测试通过\n")
            
        except Exception as e:
            print(f"   ✗ 配置模块测试失败: {e}\n")
    
    async def test_screen_capture(self):
        """测试屏幕捕获"""
        print("2. 测试屏幕捕获...")
        
        try:
            # 初始化屏幕捕获
            success = self.screen_capture.initialize()
            assert success, "屏幕捕获初始化失败"
            
            # 测试截图
            screenshot = self.screen_capture.capture()
            assert screenshot is not None, "截图失败"
            
            height, width = screenshot.shape[:2]
            print(f"   截图尺寸: {width}x{height}")
            
            # 测试保存截图
            save_success = self.screen_capture.save_screenshot("test_screenshot.jpg", screenshot)
            assert save_success, "保存截图失败"
            
            # 测试模板匹配
            # 创建一个简单的测试模板
            test_template = screenshot[100:150, 100:150]
            cv2.imwrite("test_template.png", test_template)
            
            match_result = self.screen_capture.find_template("test_template.png", threshold=0.9)
            print(f"   模板匹配: {'找到' if match_result and match_result['found'] else '未找到'}")
            
            # 测试颜色检测
            color_range = {
                'lower': (0, 0, 200),
                'upper': (180, 30, 255)
            }
            color_regions = self.screen_capture.find_color_region(color_range)
            print(f"   颜色区域检测: 找到{len(color_regions)}个区域")
            
            print("   ✓ 屏幕捕获测试通过\n")
            
        except Exception as e:
            print(f"   ✗ 屏幕捕获测试失败: {e}\n")
    
    async def test_ad_detection(self):
        """测试广告检测"""
        print("3. 测试广告检测...")
        
        try:
            # 初始化广告检测器
            success = self.ad_detector.initialize()
            assert success, "广告检测器初始化失败"
            
            # 获取测试截图
            screenshot = self.screen_capture.capture()
            assert screenshot is not None, "获取测试截图失败"
            
            # 测试广告检测
            ad_result = self.ad_detector.detect_ad(screenshot)
            print(f"   广告检测结果: {ad_result}")
            
            # 测试关闭按钮检测
            close_button = self.ad_detector.find_close_button(screenshot)
            if close_button:
                print(f"   找到关闭按钮: ({close_button['x']}, {close_button['y']})")
            else:
                print("   未找到关闭按钮")
            
            # 测试任务按钮检测
            task_buttons = self.ad_detector.find_task_buttons(screenshot)
            print(f"   找到任务按钮: {len(task_buttons)}个")
            
            # 保存检测结果
            self.ad_detector.save_detection_result(screenshot, ad_result, "test_detection_result.jpg")
            
            print("   ✓ 广告检测测试通过\n")
            
        except Exception as e:
            print(f"   ✗ 广告检测测试失败: {e}\n")
    
    async def test_task_execution(self):
        """测试任务执行"""
        print("4. 测试任务执行...")
        
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = self.task_executor.get_screen_size()
            print(f"   屏幕尺寸: {screen_width}x{screen_height}")
            
            # 获取当前鼠标位置
            mouse_x, mouse_y = self.task_executor.get_mouse_position()
            print(f"   当前鼠标位置: ({mouse_x}, {mouse_y})")
            
            # 测试鼠标移动
            test_x = screen_width // 2
            test_y = screen_height // 2
            print(f"   测试移动鼠标到: ({test_x}, {test_y})")
            
            success = await self.task_executor.move_mouse(test_x, test_y)
            assert success, "鼠标移动失败"
            
            # 等待一下让用户看到鼠标移动
            await asyncio.sleep(1)
            
            # 测试点击（在安全位置）
            print("   测试点击操作...")
            success = await self.task_executor.click_position(test_x, test_y)
            assert success, "点击操作失败"
            
            # 测试滚动
            print("   测试滚动操作...")
            success = await self.task_executor.scroll(test_x, test_y, clicks=3)
            assert success, "滚动操作失败"
            
            # 测试按键
            print("   测试按键操作...")
            success = await self.task_executor.press_key('space')
            assert success, "按键操作失败"
            
            # 测试人类行为模拟
            print("   测试人类行为模拟...")
            success = await self.task_executor.simulate_human_behavior('pause')
            assert success, "人类行为模拟失败"
            
            print("   ✓ 任务执行测试通过\n")
            
        except Exception as e:
            print(f"   ✗ 任务执行测试失败: {e}\n")
    
    async def test_bluetooth_connection(self):
        """测试蓝牙连接"""
        print("5. 测试蓝牙连接...")
        
        try:
            # 创建蓝牙控制器
            self.bluetooth = BluetoothController(self.config.bluetooth_config)
            
            # 扫描设备
            print("   扫描蓝牙设备...")
            devices = await self.bluetooth.scan_devices(timeout=5)
            print(f"   找到设备: {len(devices)}个")
            
            for device in devices:
                print(f"     - {device['name']} ({device['address']})")
            
            if devices:
                # 尝试连接第一个设备
                print("   尝试连接设备...")
                success = await self.bluetooth.connect(devices[0]['address'])
                
                if success:
                    print("   ✓ 蓝牙连接成功")
                    
                    # 测试发送命令
                    await self.bluetooth.send_command("TEST_COMMAND", {"test": "data"})
                    
                    # 等待一下
                    await asyncio.sleep(2)
                    
                    # 断开连接
                    await self.bluetooth.disconnect()
                    print("   蓝牙连接已断开")
                else:
                    print("   蓝牙连接失败")
            else:
                print("   未找到ESP32设备，跳过连接测试")
            
            print("   ✓ 蓝牙模块测试完成\n")
            
        except Exception as e:
            print(f"   ✗ 蓝牙测试失败: {e}\n")
    
    async def test_integration(self):
        """集成测试"""
        print("6. 集成测试...")
        
        try:
            print("   执行完整的任务周期...")
            
            # 截取屏幕
            screenshot = self.screen_capture.capture()
            assert screenshot is not None, "截图失败"
            
            # 检测广告
            ad_info = self.ad_detector.detect_ad(screenshot)
            print(f"   广告检测: {ad_info}")
            
            # 根据检测结果执行操作
            if ad_info['has_ad']:
                print(f"   检测到广告类型: {ad_info['type']}")
                
                if ad_info['type'] == 'video_ad':
                    print("   模拟观看视频广告...")
                    await asyncio.sleep(2)  # 模拟观看时间
                    
                    # 查找关闭按钮
                    close_button = self.ad_detector.find_close_button(screenshot)
                    if close_button:
                        print(f"   点击关闭按钮: ({close_button['x']}, {close_button['y']})")
                        await self.task_executor.click_position(close_button['x'], close_button['y'])
            else:
                print("   未检测到广告，查找任务按钮...")
                task_buttons = self.ad_detector.find_task_buttons(screenshot)
                
                if task_buttons:
                    button = task_buttons[0]
                    print(f"   点击任务按钮: ({button['x']}, {button['y']})")
                    await self.task_executor.click_position(button['x'], button['y'])
            
            print("   ✓ 集成测试通过\n")
            
        except Exception as e:
            print(f"   ✗ 集成测试失败: {e}\n")
    
    async def performance_test(self):
        """性能测试"""
        print("7. 性能测试...")
        
        try:
            # 测试截图性能
            start_time = time.time()
            for i in range(10):
                screenshot = self.screen_capture.capture()
                assert screenshot is not None
            
            screenshot_time = (time.time() - start_time) / 10
            print(f"   平均截图时间: {screenshot_time:.3f}秒")
            
            # 测试检测性能
            screenshot = self.screen_capture.capture()
            start_time = time.time()
            
            for i in range(5):
                ad_info = self.ad_detector.detect_ad(screenshot)
            
            detection_time = (time.time() - start_time) / 5
            print(f"   平均检测时间: {detection_time:.3f}秒")
            
            # 计算理论FPS
            total_time = screenshot_time + detection_time
            fps = 1.0 / total_time if total_time > 0 else 0
            print(f"   理论处理FPS: {fps:.1f}")
            
            print("   ✓ 性能测试完成\n")
            
        except Exception as e:
            print(f"   ✗ 性能测试失败: {e}\n")


async def main():
    """主函数"""
    tester = AutomationTester()
    
    print("自动化系统测试工具")
    print("请确保ESP32设备已连接并运行相应固件\n")
    
    while True:
        print("=== 测试菜单 ===")
        print("1. 完整测试")
        print("2. 测试配置模块")
        print("3. 测试屏幕捕获")
        print("4. 测试广告检测")
        print("5. 测试任务执行")
        print("6. 测试蓝牙连接")
        print("7. 集成测试")
        print("8. 性能测试")
        print("0. 退出")
        
        choice = input("\n请选择测试项目: ").strip()
        
        if choice == "1":
            await tester.test_all_modules()
            await tester.test_integration()
            await tester.performance_test()
        elif choice == "2":
            await tester.test_config()
        elif choice == "3":
            await tester.test_screen_capture()
        elif choice == "4":
            await tester.test_ad_detection()
        elif choice == "5":
            await tester.test_task_execution()
        elif choice == "6":
            await tester.test_bluetooth_connection()
        elif choice == "7":
            await tester.test_integration()
        elif choice == "8":
            await tester.performance_test()
        elif choice == "0":
            break
        else:
            print("无效选择，请重试")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    asyncio.run(main())

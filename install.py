#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动安装脚本
自动安装依赖包并进行初始化设置
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_header():
    """打印安装头部信息"""
    print("=" * 60)
    print("        蓝牙自动化脚本系统 - 自动安装程序")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    print("=" * 60)


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_pip():
    """检查pip是否可用"""
    print("检查pip...")
    
    try:
        import pip
        print("✅ pip可用")
        return True
    except ImportError:
        try:
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            print("✅ pip可用")
            return True
        except subprocess.CalledProcessError:
            print("❌ pip不可用，请先安装pip")
            return False


def install_requirements():
    """安装依赖包"""
    print("\n安装Python依赖包...")
    
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ 找不到{requirements_file}文件")
        return False
    
    try:
        # 升级pip
        print("升级pip...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True)
        
        # 安装依赖
        print("安装依赖包...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], check=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装依赖包失败: {e}")
        return False


def create_directories():
    """创建必要的目录结构"""
    print("\n创建目录结构...")
    
    directories = [
        "templates",
        "logs", 
        "screenshots",
        "configs",
        "data"
    ]
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"📁 目录已存在: {directory}")


def create_default_config():
    """创建默认配置文件"""
    print("\n创建默认配置...")
    
    config_file = "config.json"
    
    if os.path.exists(config_file):
        print(f"📄 配置文件已存在: {config_file}")
        return True
    
    try:
        from config import Config
        config = Config()
        config.save_config()
        print(f"✅ 创建默认配置文件: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False


def test_imports():
    """测试关键模块导入"""
    print("\n测试模块导入...")
    
    test_modules = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("mss", "MSS"),
        ("pyautogui", "PyAutoGUI"),
        ("bleak", "Bleak"),
        ("tkinter", "Tkinter")
    ]
    
    failed_modules = []
    
    for module_name, display_name in test_modules:
        try:
            if module_name == "PIL":
                from PIL import Image
            elif module_name == "tkinter":
                import tkinter
            else:
                __import__(module_name)
            print(f"✅ {display_name}")
        except ImportError as e:
            print(f"❌ {display_name}: {e}")
            failed_modules.append(display_name)
    
    if failed_modules:
        print(f"\n⚠️  以下模块导入失败: {', '.join(failed_modules)}")
        return False
    
    print("✅ 所有模块导入成功")
    return True


def check_system_requirements():
    """检查系统要求"""
    print("\n检查系统要求...")
    
    # 检查操作系统
    os_name = platform.system()
    if os_name in ["Windows", "Linux", "Darwin"]:
        print(f"✅ 操作系统支持: {os_name}")
    else:
        print(f"⚠️  未测试的操作系统: {os_name}")
    
    # 检查内存（简单检查）
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        
        if memory_gb >= 4:
            print(f"✅ 内存充足: {memory_gb:.1f}GB")
        else:
            print(f"⚠️  内存可能不足: {memory_gb:.1f}GB (建议4GB以上)")
    except ImportError:
        print("ℹ️  无法检查内存信息（psutil未安装）")
    
    return True


def setup_templates():
    """设置默认模板"""
    print("\n设置默认检测模板...")
    
    try:
        from setup_templates import TemplateManager
        manager = TemplateManager()
        manager.create_default_templates()
        print("✅ 默认模板创建完成")
        return True
    except Exception as e:
        print(f"❌ 创建默认模板失败: {e}")
        return False


def run_basic_test():
    """运行基本功能测试"""
    print("\n运行基本功能测试...")
    
    try:
        # 测试配置模块
        from config import Config
        config = Config()
        print("✅ 配置模块测试通过")
        
        # 测试屏幕捕获
        from screen_capture import ScreenCapture
        screen = ScreenCapture()
        if screen.initialize():
            print("✅ 屏幕捕获模块测试通过")
        else:
            print("❌ 屏幕捕获模块测试失败")
            return False
        
        # 测试广告检测
        from ad_detector import AdDetector
        detector = AdDetector()
        if detector.initialize():
            print("✅ 广告检测模块测试通过")
        else:
            print("❌ 广告检测模块测试失败")
            return False
        
        # 测试任务执行
        from task_executor import TaskExecutor
        executor = TaskExecutor()
        print("✅ 任务执行模块测试通过")
        
        print("✅ 所有基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("                    安装完成！")
    print("=" * 60)
    print("后续步骤:")
    print("1. 🔧 烧录ESP32-C3固件:")
    print("   - 打开Arduino IDE")
    print("   - 加载esp32_bluetooth_controller.ino")
    print("   - 选择ESP32C3 Dev Module")
    print("   - 烧录到设备")
    print()
    print("2. 🔌 连接硬件:")
    print("   - LED连接到GPIO2")
    print("   - 震动马达连接到GPIO4") 
    print("   - 按钮连接到GPIO0")
    print()
    print("3. ⚙️  配置系统:")
    print("   - 运行: python gui_config.py")
    print("   - 或运行: python start.py")
    print()
    print("4. 🎯 设置检测模板:")
    print("   - 运行: python setup_templates.py")
    print("   - 创建广告检测模板")
    print()
    print("5. 🧪 测试系统:")
    print("   - 运行: python test_automation.py")
    print("   - 验证所有功能正常")
    print()
    print("6. 🚀 开始使用:")
    print("   - 运行: python main.py")
    print("   - 或运行: python start.py")
    print("=" * 60)


def main():
    """主安装函数"""
    print_header()
    
    # 检查基本要求
    if not check_python_version():
        return False
    
    if not check_pip():
        return False
    
    # 检查系统要求
    check_system_requirements()
    
    # 安装依赖
    if not install_requirements():
        return False
    
    # 创建目录结构
    create_directories()
    
    # 创建默认配置
    create_default_config()
    
    # 测试模块导入
    if not test_imports():
        print("\n⚠️  部分模块导入失败，可能影响功能使用")
        response = input("是否继续安装？(y/N): ")
        if response.lower() != 'y':
            return False
    
    # 设置默认模板
    setup_templates()
    
    # 运行基本测试
    if not run_basic_test():
        print("\n⚠️  基本功能测试失败")
        response = input("是否继续完成安装？(y/N): ")
        if response.lower() != 'y':
            return False
    
    # 显示后续步骤
    show_next_steps()
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 安装成功完成！")
        else:
            print("\n❌ 安装失败，请检查错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中发生错误: {e}")
        sys.exit(1)

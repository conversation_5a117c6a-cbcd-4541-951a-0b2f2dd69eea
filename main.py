#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝牙自动化脚本主程序
用于通过ESP32-C3蓝牙设备进行自动化操作
包括截图、广告检测、自动点击等功能
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any

from bluetooth_controller import BluetoothController
from screen_capture import ScreenCapture
from ad_detector import AdDetector
from task_executor import TaskExecutor
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class AutomationController:
    """自动化控制器主类"""
    
    def __init__(self):
        self.config = Config()
        self.bluetooth = BluetoothController(self.config.bluetooth_config)
        self.screen_capture = ScreenCapture()
        self.ad_detector = AdDetector()
        self.task_executor = TaskExecutor()
        self.running = False
        self.stats = {
            'ads_watched': 0,
            'coins_earned': 0,
            'tasks_completed': 0,
            'start_time': None
        }
    
    async def initialize(self) -> bool:
        """初始化所有组件"""
        try:
            logger.info("正在初始化自动化系统...")
            
            # 初始化蓝牙连接
            if not await self.bluetooth.connect():
                logger.error("蓝牙连接失败")
                return False
            
            # 初始化屏幕捕获
            if not self.screen_capture.initialize():
                logger.error("屏幕捕获初始化失败")
                return False
            
            # 初始化广告检测器
            if not self.ad_detector.initialize():
                logger.error("广告检测器初始化失败")
                return False
            
            logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    async def start_automation(self):
        """开始自动化任务"""
        if not await self.initialize():
            return
        
        self.running = True
        self.stats['start_time'] = datetime.now()
        logger.info("开始执行自动化任务")
        
        try:
            while self.running:
                await self.execute_task_cycle()
                await asyncio.sleep(self.config.cycle_interval)
                
        except KeyboardInterrupt:
            logger.info("用户中断，正在停止...")
        except Exception as e:
            logger.error(f"执行过程中出错: {e}")
        finally:
            await self.cleanup()
    
    async def execute_task_cycle(self):
        """执行一个任务周期"""
        try:
            # 截取屏幕
            screenshot = self.screen_capture.capture()
            if screenshot is None:
                logger.warning("截图失败，跳过本次循环")
                return
            
            # 检测广告
            ad_info = self.ad_detector.detect_ad(screenshot)
            
            if ad_info['has_ad']:
                await self.handle_advertisement(ad_info, screenshot)
            else:
                await self.handle_normal_task(screenshot)
                
        except Exception as e:
            logger.error(f"任务周期执行失败: {e}")
    
    async def handle_advertisement(self, ad_info: Dict[str, Any], screenshot):
        """处理广告"""
        logger.info(f"检测到广告: {ad_info['type']}")
        
        if ad_info['type'] == 'video_ad':
            await self.watch_video_ad(ad_info, screenshot)
        elif ad_info['type'] == 'banner_ad':
            await self.close_banner_ad(ad_info, screenshot)
        elif ad_info['type'] == 'popup_ad':
            await self.close_popup_ad(ad_info, screenshot)
    
    async def watch_video_ad(self, ad_info: Dict[str, Any], screenshot):
        """观看视频广告"""
        logger.info("开始观看视频广告")
        
        # 等待广告播放完成
        watch_duration = ad_info.get('duration', self.config.default_ad_duration)
        
        # 发送开始观看信号到ESP32
        await self.bluetooth.send_command('START_WATCH_AD')
        
        # 等待广告播放
        await asyncio.sleep(watch_duration)
        
        # 检测关闭按钮并点击
        close_button = self.ad_detector.find_close_button(screenshot)
        if close_button:
            await self.task_executor.click_position(close_button['x'], close_button['y'])
            logger.info("已点击广告关闭按钮")
        
        # 更新统计
        self.stats['ads_watched'] += 1
        self.stats['coins_earned'] += ad_info.get('reward', 10)
        
        # 发送完成信号到ESP32
        await self.bluetooth.send_command('AD_COMPLETED')
    
    async def close_banner_ad(self, ad_info: Dict[str, Any], screenshot):
        """关闭横幅广告"""
        close_button = self.ad_detector.find_close_button(screenshot)
        if close_button:
            await self.task_executor.click_position(close_button['x'], close_button['y'])
            logger.info("已关闭横幅广告")
    
    async def close_popup_ad(self, ad_info: Dict[str, Any], screenshot):
        """关闭弹窗广告"""
        close_button = self.ad_detector.find_close_button(screenshot)
        if close_button:
            await self.task_executor.click_position(close_button['x'], close_button['y'])
            logger.info("已关闭弹窗广告")
    
    async def handle_normal_task(self, screenshot):
        """处理正常任务"""
        # 检测任务按钮
        task_buttons = self.ad_detector.find_task_buttons(screenshot)
        
        for button in task_buttons:
            if button['type'] == 'watch_ad_button':
                await self.task_executor.click_position(button['x'], button['y'])
                logger.info("点击了观看广告按钮")
                self.stats['tasks_completed'] += 1
                break
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if self.stats['start_time']:
            runtime = datetime.now() - self.stats['start_time']
            self.stats['runtime'] = str(runtime)
        return self.stats.copy()
    
    async def cleanup(self):
        """清理资源"""
        logger.info("正在清理资源...")
        self.running = False
        
        if self.bluetooth:
            await self.bluetooth.disconnect()
        
        if self.screen_capture:
            self.screen_capture.cleanup()
        
        logger.info("资源清理完成")


async def main():
    """主函数"""
    controller = AutomationController()
    
    try:
        await controller.start_automation()
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        # 显示统计信息
        stats = controller.get_stats()
        logger.info(f"执行统计: {stats}")


if __name__ == "__main__":
    asyncio.run(main())

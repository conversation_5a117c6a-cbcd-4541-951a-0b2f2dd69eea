# 蓝牙自动化脚本系统

基于ESP32-C3蓝牙设备的自动化脚本系统，用于各大平台的看广告获得金币任务。

## 功能特性

- 🔗 **蓝牙通信**: 与ESP32-C3设备进行稳定的蓝牙通信
- 📱 **屏幕截图**: 实时捕获屏幕内容进行分析
- 🤖 **广告检测**: 智能识别视频广告、横幅广告、弹窗广告
- 🎯 **自动操作**: 自动点击、滑动、关闭广告等操作
- 🛡️ **安全保护**: 多重安全机制，防止误操作
- 📊 **统计监控**: 实时统计收益和运行状态
- ⚙️ **配置管理**: 灵活的配置系统，支持多平台

## 系统架构

```
┌─────────────────┐    蓝牙通信    ┌─────────────────┐
│   PC端脚本      │ ◄──────────► │   ESP32-C3      │
│                 │              │   蓝牙控制器     │
├─────────────────┤              ├─────────────────┤
│ • 屏幕截图      │              │ • 震动反馈      │
│ • 广告检测      │              │ • 状态指示      │
│ • 任务执行      │              │ • 按钮控制      │
│ • 配置管理      │              │ • 电量监控      │
└─────────────────┘              └─────────────────┘
```

## 硬件要求

### ESP32-C3开发板
- ESP32-C3芯片（支持蓝牙5.0）
- LED指示灯（GPIO2）
- 震动马达（GPIO4）
- 用户按钮（GPIO0）
- 可选：电池电量检测电路

### PC端要求
- Windows 10/11 或 Linux
- Python 3.8+
- 蓝牙适配器（支持BLE）
- 摄像头或屏幕截图权限

## 安装步骤

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd 蓝牙自动化脚本

# 安装Python依赖
pip install -r requirements.txt
```

### 2. ESP32-C3固件烧录

1. 安装Arduino IDE
2. 添加ESP32开发板支持
3. 安装必要库：
   - ArduinoJson
   - BluetoothSerial
4. 打开`esp32_bluetooth_controller.ino`
5. 选择开发板：ESP32C3 Dev Module
6. 烧录固件到ESP32-C3

### 3. 硬件连接

```
ESP32-C3 引脚连接：
├── GPIO2  → LED（状态指示）
├── GPIO4  → 震动马达
├── GPIO0  → 按钮（上拉）
├── 3.3V   → 电源正极
└── GND    → 电源负极
```

## 使用指南

### 1. 首次配置

```bash
# 运行配置工具
python gui_config.py
```

在配置界面中：
- 设置蓝牙设备名称
- 配置检测参数
- 添加目标平台
- 设置安全限制

### 2. 创建检测模板

```bash
# 运行模板设置工具
python setup_templates.py
```

操作步骤：
1. 选择"从屏幕截图创建模板"
2. 截取包含广告元素的屏幕
3. 框选关键区域（如关闭按钮）
4. 保存模板并命名

### 3. 系统测试

```bash
# 运行测试脚本
python test_automation.py
```

测试项目：
- 蓝牙连接测试
- 屏幕捕获测试
- 广告检测测试
- 任务执行测试

### 4. 启动自动化

```bash
# 启动主程序
python main.py
```

## 配置说明

### 主要配置文件：`config.json`

```json
{
  "bluetooth": {
    "device_name": "ESP32-C3",
    "scan_timeout": 10,
    "auto_reconnect": true
  },
  "automation": {
    "cycle_interval": 2.0,
    "default_ad_duration": 30,
    "action_interval": 0.5
  },
  "detection": {
    "confidence_threshold": 0.7,
    "template_match_threshold": 0.8
  },
  "safety": {
    "enable_failsafe": true,
    "max_continuous_failures": 5,
    "human_behavior_simulation": true
  }
}
```

### 平台配置示例

```json
{
  "platforms": [
    {
      "name": "抖音极速版",
      "package_name": "com.ss.android.ugc.aweme.lite",
      "ad_patterns": ["video_ad", "banner_ad"],
      "reward_per_ad": 10,
      "max_ads_per_session": 20
    }
  ]
}
```

## 核心模块说明

### 1. 蓝牙控制器 (`bluetooth_controller.py`)
- 设备扫描和连接
- 命令发送和状态接收
- 自动重连机制

### 2. 屏幕捕获 (`screen_capture.py`)
- 实时屏幕截图
- 图像预处理
- 模板匹配
- 颜色检测

### 3. 广告检测器 (`ad_detector.py`)
- 视频广告检测
- 横幅广告检测
- 弹窗广告检测
- 按钮识别

### 4. 任务执行器 (`task_executor.py`)
- 鼠标点击和移动
- 键盘输入
- 滑动手势
- 人类行为模拟

## 安全机制

### 1. 故障保护
- 鼠标移到左上角自动停止
- 连续失败次数限制
- 紧急停止快捷键

### 2. 行为模拟
- 随机延迟
- 鼠标轨迹模拟
- 操作间隔控制

### 3. 使用限制
- 每日广告数量限制
- 运行时间限制
- 定期休息机制

## 故障排除

### 常见问题

1. **蓝牙连接失败**
   - 检查ESP32是否正常工作
   - 确认蓝牙适配器驱动
   - 重启蓝牙服务

2. **广告检测不准确**
   - 更新检测模板
   - 调整置信度阈值
   - 检查屏幕分辨率

3. **操作执行失败**
   - 检查屏幕权限
   - 调整操作间隔
   - 确认坐标准确性

### 日志分析

```bash
# 查看运行日志
tail -f automation.log

# 查看错误日志
grep ERROR automation.log
```

## 开发指南

### 添加新平台支持

1. 在配置中添加平台信息
2. 创建平台特定的检测模板
3. 调整检测参数
4. 测试验证

### 自定义检测算法

```python
# 继承AdDetector类
class CustomAdDetector(AdDetector):
    def detect_custom_ad(self, screenshot):
        # 实现自定义检测逻辑
        pass
```

### 扩展蓝牙命令

```python
# 在ESP32固件中添加新命令
bool handleCustomCommand(JsonObject data) {
    // 实现自定义命令逻辑
    return true;
}
```

## 注意事项

⚠️ **重要提醒**：
- 本系统仅供学习和研究使用
- 请遵守各平台的使用条款
- 不要用于商业用途
- 注意保护个人隐私和数据安全

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**免责声明**：使用本软件的风险由用户自行承担。开发者不对任何直接或间接损失负责。

{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"$ref '{0}' in '{1}' can not be resolved.": "'{1}' 中的 $ref '{0}' 無法解析。", "<empty>": "<empty>", "A default value. Used by suggestions.": "預設值。由建議使用。", "A descriptive title of the element.": "元素的描述性標題。", "A long description of the element. Used in hover menus and suggestions.": "元素的詳細描述。用於暫留功能表和建議。", "A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object.": "屬性名稱與屬性名稱陣列或結構描述的對應。屬性名稱陣列表示索引碼中指定的屬性取決於出現於物件之陣列中的屬性，才可成為有效。如果值是結構描述，則只有索引碼中的屬性出現在物件上時，結構描述才會套用至物件。", "A map of property names to schemas for each property.": "每個屬性之屬性名稱與結構描述的對應。", "A map of regular expressions on property names to schemas for matching properties.": "屬性名稱上規則運算式與結構描述的對應，用於比對屬性。", "A number that should cleanly divide the current value (i.e. have no remainder).": "應該將目前值整除 (即沒有餘數) 的數值。", "A regular expression to match the string against. It is not implicitly anchored.": "用來比對字串的規則運算式。未隱含錨定。", "A schema which must not match.": "不可相符的結構描述。", "A unique identifier for the schema.": "結構描述的唯一識別碼。", "An array instance is valid against \"contains\" if at least one of its elements is valid against the given schema.": "如果陣列執行個體的至少其中一個元素對指定的結構描述有效，則陣列執行個體對 \"contains\" 有效。", "An array of schemas, all of which must match.": "結構描述的陣列，全部必須相符。", "An array of schemas, exactly one of which must match.": "結構描述陣列，其中一個必須相符。", "An array of schemas, where at least one must match.": "結構描述的陣列，其中至少必須有一個相符。", "An array of strings that lists the names of all properties required on this object.": "字串的陣列，列出此物件上所需的所有屬性的名稱。", "An instance validates successfully against this keyword if its value is equal to the value of the keyword.": "如果執行個體的值等於關鍵字的值，則執行個體針對此關鍵字的驗證成功。", "Array does not contain required item.": "陣列未包含必要的項目。", "Array has duplicate items.": "陣列有重複的項目。", "Array has too few items that match the contains contraint. Expected {0} or more.": "陣列所具有符合包含條件約束的項目太少。預期為 {0} 個或更多個。", "Array has too few items. Expected {0} or more.": "陣列的項目太少。必須是 {0} 以上。", "Array has too many items according to schema. Expected {0} or fewer.": "根據結構描述，陣列有太多項目。預期為 {0} 個或以下。", "Array has too many items that match the contains contraint. Expected {0} or less.": "陣列所具有符合包含條件約束的項目太多。預期為 {0} 個或更少個。", "Array has too many items. Expected {0} or fewer.": "陣列有太多項目。預期為 {0} 個或以下。", "Colon expected": "預期為冒號", "Comments are not permitted in JSON.": "JSON 中不允許註解。", "Comments from schema authors to readers or maintainers of the schema.": "結構描述作者對結構描述讀取者或維護者的註解。", "Configure": "設定", "Configured by extension: {0}": "由延伸模組設定: {0}", "Configured in user settings": "在使用者設定中設定", "Configured in workspace settings": "在工作區設定中設定", "Default value": "預設值", "Describes the content encoding of a string property.": "描述字串屬性的內容編碼。", "Describes the format expected for the value.": "描述值的預期格式。", "Describes the media type of a string property.": "描述字串屬性的媒體類型。", "Downloading schemas is disabled through setting '{0}'": "已透過設定 '{0}' 停用下載結構描述", "Downloading schemas is disabled. Click to configure.": "已停用下載結構描述。按一下以進行設定。", "Draft-03 schemas are not supported.": "不支援 Draft-03 結構描述。", "Duplicate anchor declaration: '{0}'": "重複的錨點宣告: '{0}'", "Duplicate object key": "重複的物件索引碼", "Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail.": "結構描述或布林值。如果是結構描述，則用來驗證與 'properties' 或 'patternProperties' 不符的所有屬性。如果為 False，則任何不符的屬性都會導致此結構描述失敗。", "Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types.": "可以是其中一個基本結構描述類型 (數字、整數、Null、陣列、物件、布林值、字串) 的字串，或指定這些類型子集的字串陣列。", "End of file expected.": "必須有檔案結尾。", "Expected a JSON object, array or literal.": "預期為 JSON 物件、陣列或文字。", "Expected comma": "預期為逗號", "Expected comma or closing brace": "必須是逗號或右大括弧", "Expected comma or closing bracket": "預期為逗號或右中括弧", "Failed to sort the JSONC document, please consider opening an issue.": "無法排序 JSONC 文件，請考慮開啟問題。", "For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail.": "針對陣列，只有在項目設定為陣列時。如果是結構描述，則此結構描述會驗證項目陣列所指定項目之後的項目。如果為 False，則其他項目將導致驗證失敗。", "For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on.": "針對陣列。可以是結構描述，用來驗證每個元素或結構描述的陣列，用來依序驗證每個項目 (第一個結構描述將驗證第一個元素，第二個結構描述將驗證第二個元素，以此類推)。", "If all of the items in the array must be unique. Defaults to false.": "如果陣列中的所有項目都必須是唯一。預設為 false。", "If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema.": "如果執行個體是物件，則此關鍵字會驗證執行個體中的每個屬性名稱是否可針對提供的結構描述進行驗證。", "Incorrect type. Expected \"{0}\".": "不正確的類型。必須是 \"{0}\"。", "Incorrect type. Expected one of {0}.": "不正確的類型。必須是其中一個 {0}。", "Indicates that the value of the instance is managed exclusively by the owning authority.": "指示執行個體的值由擁有授權單位專門管理。", "Invalid characters in string. Control characters must be escaped.": "字串中的字元無效。控制字元必須逸出。", "Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA.": "色彩格式無效。請使用 #RGB、#RGBA、#RRGGBB 或 #RRGGBBAA。", "Invalid escape character in string.": "字串中的逸出字元無效。", "Invalid number format.": "數字格式無效。", "Invalid unicode sequence in string.": "字串中的 Unicode 序列無效。", "Item does not match any validation rule from the array.": "項目不符合來自陣列的任何驗證規則。", "JSON Language Server": "JSON 語言伺服器", "JSON Outline Status": "JSON 大綱狀態", "JSON Validation Status": "JSON 驗證狀態", "JSON schema cache cleared.": "JSON 結構描述快取已清除。", "JSON schema configured": "已設定 JSON 結構描述", "JSON: Schema Resolution Error": "JSON: 結構描述解析錯誤", "Learn more about JSON schema configuration...": "深入了解 JSON 結構描述設定...", "Loading JSON info": "載入 JSON 資訊", "Makes the maximum property exclusive.": "排除最大屬性。", "Makes the minimum property exclusive.": "排除最小屬性。", "Matches a schema that is not allowed.": "比對不允許的結構描述。", "Matches multiple schemas when only one must validate.": "當只有一個必須驗證時，比對多個結構描述。", "Missing property \"{0}\".": "遺失屬性 \"{0}\"。", "New array": "新增陣列", "New object": "新增物件", "No Schema Validation": "無結構描述驗證", "No schema configured for this file": "未設定此檔案的結構描述", "Not used for validation. Place subschemas here that you wish to reference inline with $ref.": "未用於驗證。將您要使用 $ref 內嵌參考的子結構描述放在這裡。", "Object has fewer properties than the required number of {0}": "物件的屬性少於所需的物件數目 {0}", "Object has more properties than limit of {0}.": "物件的屬性超過 {0} 個的限制。", "Object is missing property {0} required by property {1}.": "物件遺失屬性 {1} 所需的屬性 {0}。", "Open Extension": "開啟延伸模組", "Open Settings": "開啟設定", "Outline": "大綱", "Problem reading content from '{0}': UTF-8 with BOM detected, only UTF 8 is allowed.": "從 '{0}' 讀取內容時發生問題: 偵測到帶 BOM 的 UTF-8，但只允許 UTF-8。", "Problems loading reference '{0}': {1}": "載入參考 '{0}' 時發生問題: {1}", "Property expected": "預期為屬性", "Property keys must be doublequoted": "屬性索引鍵必須使用雙引號括住", "Property {0} is not allowed.": "不允許屬性 {0}。", "Reference a definition hosted on any location.": "參考託管於任何位置的定義。", "Sample JSON values associated with a particular schema, for the purpose of illustrating usage.": "為了說明使用方式，與特定結構描述相關聯的範例 JSON 值。", "Schema Validated": "結構描述已驗證", "Select the schema to use for {0}": "選取要用於 {0} 的結構描述", "Show Schemas": "顯示結構描述", "Sort JSON": "排序 JSON", "String does not match the pattern of \"{0}\".": "字串不符合 \"{0}\" 的模式。", "String is longer than the maximum length of {0}.": "字串超過長度 {0} 的上限。", "String is not a RFC3339 date-time.": "字串不是 RFC3339 日期時間。", "String is not a RFC3339 date.": "字串不是 RFC3339 日期。", "String is not a RFC3339 time.": "字串不是 RFC3339 時間。", "String is not a URI: {0}": "字串不是 URI: {0}", "String is not a hostname.": "字串不是主機名稱。", "String is not an IPv4 address.": "字串不是 IPv4 位址。", "String is not an IPv6 address.": "字串不是 IPv6 位址。", "String is not an e-mail address.": "字串不是電子郵件地址。", "String is shorter than the minimum length of {0}.": "字串短於最小長度 {0}。", "The \"else\" subschema is used for validation when the \"if\" subschema fails.": "\"if\" 子結構描述失敗時，會使用 \"else\" 子結構描述進行驗證。", "The \"if\" subschema is used for validation when the \"if\" subschema succeeds.": "\"if\" 子結構描述成功時，會使用 \"if\" 子結構描述進行驗證。", "The maximum length of a string.": "字串的長度上限。", "The maximum number of items that can be inside an array. Inclusive.": "可以在陣列內的項目數目上限。包含。", "The maximum number of properties an object can have. Inclusive.": "物件可以擁有的屬性數目上限。包含。", "The maximum numerical value, inclusive by default.": "數值上限，預設為包含。", "The minimum length of a string.": "字串的最小長度。", "The minimum number of items that can be inside an array. Inclusive.": "可以在陣列內的項目數目下限。包含。", "The minimum number of properties an object can have. Inclusive.": "物件可以擁有的屬性數目下限。包含。", "The minimum numerical value, inclusive by default.": "數值下限，預設為包含。", "The schema to verify this document against.": "用來驗證此文件的結構描述。", "The schema uses meta-schema features ({0}) that are not yet supported by the validator.": "結構描述使用驗證程式尚未支援的 meta-schema 功能 ({0})。", "The set of literal values that are valid.": "一組有效的文字值。", "The validation outcome of the \"if\" subschema controls which of the \"then\" or \"else\" keywords are evaluated.": "\"if\" 子結構描述的驗證結果會控制要評估的 \"then\" 或 \"else\" 關鍵字。", "Trailing comma": "後置逗號", "URI expected.": "必須有 URI。", "URI is expected.": "必須是 URI。", "URI with a scheme is expected.": "必須是具有配置的 URI。", "Unable to compute used schemas: No document": "無法計算使用的結構描述: 沒有文件", "Unable to compute used schemas: {0}": "無法計算使用的結構描述: {0}", "Unable to load schema from '{0}'. No schema request service available": "無法從 '{0}' 載入結構描述。沒有可用的結構描述要求服務", "Unable to load schema from '{0}': No content.": "無法從 '{0}' 載入結構描述: 沒有內容。", "Unable to load schema from '{0}': {1}.": "無法從 '{0}' 載入結構描述: {1}。", "Unable to load {0}": "無法載入 {0}", "Unable to parse content from '{0}': Parse error at offset {1}.": "無法剖析來自 '{0}' 的內容: 在位移 {1} 剖析錯誤。", "Unable to resolve schema. Click to retry.": "無法解析結構描述。按一下以重試。", "Unexpected end of comment.": "未預期的註解結尾。", "Unexpected end of number.": "未預期的數字結尾。", "Unexpected end of string.": "非預期的字串結尾。", "Value expected": "預期為值", "Value is above the exclusive maximum of {0}.": "值高於 {0} 的排除最大值。", "Value is above the maximum of {0}.": "值高於 {0} 的最大值。", "Value is below the exclusive minimum of {0}.": "值低於 {0} 的排除最小值。", "Value is below the minimum of {0}.": "值低於 {0} 的最小值。", "Value is deprecated": "值已取代", "Value is not accepted. Valid values: {0}.": "不接受值。有效值: {0}。", "Value is not divisible by {0}.": "值無法被 {0} 除。", "Value must be {0}.": "值必須為 {0}。", "multiple JSON schemas configured": "已設定多個 JSON 結構描述", "no JSON schema configured": "未設定 JSON 結構描述", "only {0} document symbols shown for performance reasons": "只顯示 {0} 個文件符號 (效能原因)"}, "package": {"description": "為 JSON 檔案提供豐富的語言支援", "displayName": "JSON 語言功能", "json.clickToRetry": "按一下以重試。", "json.colorDecorators.enable.deprecationMessage": "設定 `json.colorDecorators.enable` 已淘汰，將改為 `editor.colorDecorators`。", "json.colorDecorators.enable.desc": "啟用或停用彩色裝飾項目", "json.command.clearCache": "清除結構描述快取", "json.command.sort": "排序文件", "json.enableSchemaDownload.desc": "若啟用，則可以從 http 和 https 位置擷取 JSON 結構描述。", "json.format.enable.desc": "啟用/停用預設 JSON 格式器", "json.format.keepLines.desc": "格式化時保留所有現有的新行。", "json.maxItemsComputed.desc": "大綱符號和計算摺疊區域的數目上限 (因效能原因限制)。", "json.maxItemsExceededInformation.desc": "超過大綱符號和摺疊區域的數目上限時顯示通知。", "json.schemaResolutionErrorMessage": "無法解析結構描述。", "json.schemas.desc": "在結構描述與目前專案的 JSON 檔案之間建立關聯。", "json.schemas.fileMatch.desc": "各種在將 JSON 檔案解析為結構描述時，用以比對的檔案模式。可使用 '*' 和 '**' 作為萬用字元，也可在開頭使用 '!' 來定義排除模式。只要有一個比對模式，就會比對檔案，而且最後一個比對模式不得為排除模式。", "json.schemas.fileMatch.item.desc": "將 JSON 檔案解析為結構描述時，可包含要比對之 '*' 和 '**' 的檔案模式。以 '!' 開頭時，會定義排除模式。", "json.schemas.schema.desc": "指定 URL 的結構描述定義。只須提供結構描述以避免存取結構描述 URL。", "json.schemas.url.desc": "結構描述的 URL 或絕對檔案路徑。可以是工作區與工作區資料夾設定中的相對路徑 (開頭為 './')。", "json.tracing.desc": "追蹤 VS Code 與 JSON 語言伺服器之間的通訊。", "json.validate.enable.desc": "啟用/停用 JSON 驗證。"}}}
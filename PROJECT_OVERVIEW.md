# 蓝牙自动化项目总览

这是一个完整的蓝牙自动化解决方案，包含PC端脚本、Android应用和ESP32-C3硬件控制器。

## 🎯 项目目标

开发一个基于ESP32-C3蓝牙设备的自动化系统，用于各大平台的看广告获得金币任务。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    蓝牙自动化系统架构                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    蓝牙通信    ┌─────────────────────────┐  │
│  │   手机端    │ ◄──────────► │      ESP32-C3           │  │
│  │  Android    │              │     蓝牙控制器           │  │
│  │    应用     │              │                         │  │
│  └─────────────┘              └─────────────────────────┘  │
│         │                                   │              │
│         │                                   │              │
│  ┌─────────────┐                   ┌─────────────────────┐  │
│  │  无障碍服务  │                   │    硬件反馈         │  │
│  │  屏幕截图   │                   │    • LED指示        │  │
│  │  广告检测   │                   │    • 震动马达       │  │
│  │  自动操作   │                   │    • 按钮控制       │  │
│  └─────────────┘                   └─────────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
蓝牙自动化项目/
├── 📱 Android应用 (android_app/)
│   ├── MainActivity.java              # 主界面
│   ├── AutomationController.java      # 自动化控制
│   ├── BluetoothController.java       # 蓝牙通信
│   ├── ScreenCaptureService.java      # 屏幕截图
│   ├── AdDetectionService.java        # 广告检测
│   ├── AutomationAccessibilityService.java # 无障碍服务
│   └── res/                           # 资源文件
│
├── 💻 PC端脚本 (可选)
│   ├── main.py                        # 主程序
│   ├── bluetooth_controller.py        # 蓝牙通信
│   ├── screen_capture.py             # 屏幕截图
│   ├── ad_detector.py                # 广告检测
│   ├── task_executor.py              # 任务执行
│   └── config.py                     # 配置管理
│
├── 🔌 ESP32固件
│   └── esp32_bluetooth_controller.ino # Arduino固件
│
└── 📄 文档
    ├── README.md                      # 主要说明
    ├── README_ANDROID.md              # Android应用说明
    └── PROJECT_OVERVIEW.md            # 项目总览
```

## 🚀 核心功能

### 1. Android应用功能
- ✅ **蓝牙连接管理**: 自动扫描、连接ESP32设备
- ✅ **屏幕截图服务**: 实时捕获手机屏幕
- ✅ **智能广告检测**: 基于OpenCV的图像识别
- ✅ **无障碍自动化**: 模拟点击、滑动等操作
- ✅ **统计监控**: 实时显示收益和运行状态
- ✅ **安全保护**: 多重安全机制防止误操作

### 2. ESP32-C3硬件功能
- ✅ **蓝牙通信**: 与手机稳定连接
- ✅ **状态指示**: LED显示工作状态
- ✅ **震动反馈**: 操作确认和提醒
- ✅ **按钮控制**: 手动启停功能
- ✅ **电量监控**: 实时电池状态
- ✅ **命令处理**: 执行手机端指令

### 3. PC端脚本功能（可选）
- ✅ **跨平台支持**: Windows/Linux/macOS
- ✅ **图形配置界面**: 易于设置和管理
- ✅ **模板管理**: 自定义检测模板
- ✅ **批量处理**: 支持多任务并行
- ✅ **日志记录**: 详细的运行日志

## 🎮 使用场景

### 主要应用
1. **短视频平台**: 抖音极速版、快手极速版等
2. **电商平台**: 淘宝、京东、拼多多等
3. **社交应用**: 微信小程序、QQ等
4. **游戏应用**: 各种签到、看广告获得奖励的游戏

### 自动化任务
- 🎯 自动观看视频广告
- 🎯 自动点击获得奖励
- 🎯 自动关闭弹窗广告
- 🎯 自动完成签到任务
- 🎯 自动收集金币奖励

## 🔧 技术栈

### Android应用
- **开发语言**: Java
- **最低版本**: Android 7.0 (API 24)
- **核心库**: 
  - OpenCV (图像处理)
  - Accessibility Service (无障碍)
  - MediaProjection (屏幕录制)
  - Bluetooth LE (蓝牙通信)

### ESP32-C3固件
- **开发环境**: Arduino IDE
- **核心库**:
  - BluetoothSerial (蓝牙串口)
  - ArduinoJson (JSON处理)
  - WiFi (网络功能)

### PC端脚本
- **开发语言**: Python 3.8+
- **核心库**:
  - OpenCV (图像处理)
  - PyAutoGUI (自动化操作)
  - Bleak (蓝牙通信)
  - Tkinter (图形界面)

## 📊 性能指标

### 检测性能
- **检测准确率**: >90%
- **响应时间**: <500ms
- **误操作率**: <5%
- **电池续航**: 8-12小时

### 系统要求
- **Android设备**: 2GB+ RAM, 蓝牙4.0+
- **ESP32-C3**: 标准开发板即可
- **PC设备**: 4GB+ RAM, Python 3.8+

## 🛡️ 安全机制

### 1. 操作安全
- **故障保护**: 异常情况自动停止
- **频率限制**: 防止过于频繁操作
- **权限控制**: 最小权限原则
- **用户确认**: 关键操作需要确认

### 2. 隐私保护
- **本地处理**: 所有数据本地处理
- **无网络传输**: 不上传个人信息
- **权限透明**: 明确说明权限用途
- **数据加密**: 敏感数据加密存储

### 3. 使用限制
- **时间限制**: 每日使用时间限制
- **次数限制**: 操作次数限制
- **休息机制**: 强制休息时间
- **监控告警**: 异常行为告警

## 🚀 快速开始

### 1. 硬件准备
```
ESP32-C3开发板 × 1
LED灯 × 1
震动马达 × 1
按钮 × 1
面包板和杜邦线
```

### 2. 软件安装
```bash
# Android应用
1. 下载APK文件
2. 安装到手机
3. 授予必要权限

# ESP32固件
1. 安装Arduino IDE
2. 烧录固件到ESP32
3. 连接硬件组件
```

### 3. 使用流程
```
1. 启动Android应用
2. 连接ESP32设备
3. 启用无障碍服务
4. 打开目标应用
5. 开始自动化任务
```

## 📈 发展规划

### 短期目标 (1-3个月)
- [ ] 完善Android应用功能
- [ ] 优化检测算法准确率
- [ ] 增加更多平台支持
- [ ] 改进用户界面体验

### 中期目标 (3-6个月)
- [ ] 开发iOS版本应用
- [ ] 添加云端配置同步
- [ ] 实现多设备协同
- [ ] 增加机器学习检测

### 长期目标 (6-12个月)
- [ ] 商业化产品开发
- [ ] 开放API接口
- [ ] 社区生态建设
- [ ] 国际化支持

## 🤝 贡献指南

### 参与方式
1. **代码贡献**: 提交Pull Request
2. **问题反馈**: 提交Issue报告
3. **文档完善**: 改进项目文档
4. **测试验证**: 在不同设备上测试

### 开发规范
- 遵循代码规范和注释标准
- 提交前进行充分测试
- 保持向后兼容性
- 更新相关文档

## ⚠️ 免责声明

1. **仅供学习研究**: 本项目仅用于技术学习和研究
2. **遵守法律法规**: 使用时请遵守当地法律法规
3. **平台条款**: 遵守各平台的使用条款
4. **风险自担**: 使用风险由用户自行承担
5. **无商业保证**: 不提供商业级别的技术支持

## 📞 联系方式

- **项目仓库**: [GitHub链接]
- **技术支持**: [邮箱地址]
- **问题反馈**: [Issue页面]
- **讨论交流**: [社区论坛]

---

**最后更新**: 2024年1月
**项目版本**: v1.0.0
**维护状态**: 积极维护中

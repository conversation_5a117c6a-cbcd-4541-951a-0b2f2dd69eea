# Android蓝牙自动化应用

这是一个Android应用，用于控制ESP32-C3蓝牙设备进行自动化操作。

## 📱 应用功能

### 核心功能
- 🔗 **蓝牙连接**: 自动扫描并连接ESP32-C3设备
- 📱 **屏幕截图**: 实时捕获手机屏幕内容
- 🤖 **广告检测**: 智能识别各种类型的广告
- 🎯 **自动操作**: 通过无障碍服务执行点击、滑动等操作
- 📊 **统计监控**: 实时显示运行状态和收益统计

### 支持的平台
- 抖音极速版
- 快手极速版
- 微信小程序
- 淘宝、京东等电商平台

## 🚀 快速开始

### 1. 环境要求
- Android 7.0 (API 24) 或更高版本
- 支持蓝牙4.0+的设备
- 至少2GB RAM
- ESP32-C3开发板（已烧录固件）

### 2. 安装步骤

#### 方法一：直接安装APK
1. 下载预编译的APK文件
2. 在手机设置中允许安装未知来源应用
3. 安装APK文件

#### 方法二：从源码编译
1. 安装Android Studio
2. 下载OpenCV Android SDK
3. 导入项目并编译

```bash
# 克隆项目
git clone <repository-url>
cd android_app

# 使用Android Studio打开项目
# 或使用命令行编译
./gradlew assembleDebug
```

### 3. 权限设置

应用需要以下权限：

#### 必需权限
- **蓝牙权限**: 连接ESP32设备
- **位置权限**: 蓝牙扫描需要
- **无障碍服务**: 执行自动化操作
- **屏幕录制权限**: 截图和检测

#### 设置步骤
1. 打开应用后，按提示授予各项权限
2. 进入系统设置 → 无障碍 → 启用"蓝牙自动化服务"
3. 授予屏幕录制权限

### 4. 首次使用

1. **连接ESP32设备**
   - 确保ESP32已开机并可被发现
   - 点击"连接设备"按钮
   - 从列表中选择ESP32设备

2. **配置目标应用**
   - 打开要自动化的应用（如抖音极速版）
   - 返回自动化应用
   - 点击"开始任务"

3. **开始自动化**
   - 应用会自动检测广告并执行操作
   - 可以在日志区域查看运行状态
   - 统计信息会实时更新

## 🔧 详细配置

### 蓝牙连接设置

```java
// 在MainActivity中配置蓝牙参数
private static final String ESP32_NAME = "ESP32-C3-蓝牙控制器";
private static final UUID ESP32_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
```

### 检测参数调整

```java
// 在AdDetectionService中调整检测阈值
private static final double CONFIDENCE_THRESHOLD = 0.7;
private static final int MIN_BUTTON_SIZE = 50;
private static final int MAX_BUTTON_SIZE = 300;
```

### 自动化行为配置

```java
// 在AutomationController中设置操作参数
private static final int CLICK_DURATION = 100;
private static final int SWIPE_DURATION = 500;
private static final int ACTION_DELAY = 1000;
```

## 📋 使用说明

### 主界面功能

1. **设备状态卡片**
   - 显示ESP32连接状态
   - 电池电量信息
   - 连接/断开按钮

2. **控制按钮区域**
   - 开始任务：启动自动化
   - 停止任务：停止自动化
   - 测试截图：验证截图功能

3. **统计信息**
   - 已观看广告数量
   - 获得金币总数
   - 运行时间统计

4. **日志显示**
   - 实时显示运行日志
   - 错误信息提示
   - 操作历史记录

### 操作流程

```
1. 启动应用 → 2. 连接ESP32 → 3. 授予权限 → 4. 打开目标应用 → 5. 开始自动化
```

### 常用操作

- **手动停止**: 点击停止按钮或按ESP32上的物理按钮
- **查看日志**: 滚动日志区域查看详细信息
- **清除日志**: 点击日志区域的清除按钮
- **重新连接**: 断开后重新点击连接按钮

## ⚙️ 高级功能

### 自定义检测模板

```java
// 添加新的广告检测模式
public void addCustomDetectionPattern(String appPackage, DetectionPattern pattern) {
    // 实现自定义检测逻辑
}
```

### 批量操作

```java
// 执行复杂的手势序列
AutomationController.GestureSequence sequence = new AutomationController.GestureSequence();
sequence.addClick(100, 200);
sequence.addWait(1000);
sequence.addSwipe(100, 300, 500, 300);
automationController.performGestureSequence(sequence);
```

### 数据导出

```java
// 导出统计数据
public void exportStats() {
    // 将统计信息保存到文件
}
```

## 🛠️ 故障排除

### 常见问题

1. **蓝牙连接失败**
   - 检查ESP32是否正常工作
   - 确认设备已配对
   - 重启蓝牙服务

2. **无障碍服务无法启用**
   - 进入系统设置手动启用
   - 检查应用权限
   - 重启应用

3. **截图权限被拒绝**
   - 重新授予屏幕录制权限
   - 检查系统版本兼容性
   - 重启设备

4. **检测不准确**
   - 调整检测阈值
   - 更新检测模板
   - 检查屏幕分辨率

### 调试模式

```java
// 启用调试日志
private static final boolean DEBUG_MODE = true;

if (DEBUG_MODE) {
    Log.d(TAG, "调试信息: " + debugInfo);
}
```

### 性能优化

- 降低截图频率
- 减少检测区域
- 优化图像处理算法
- 使用硬件加速

## 📱 兼容性

### 测试设备
- ✅ 小米、华为、OPPO、vivo主流机型
- ✅ 三星Galaxy系列
- ✅ 一加、魅族等品牌
- ⚠️ 部分定制系统可能需要额外配置

### 系统版本
- ✅ Android 7.0 - 14.0
- ⚠️ Android 6.0及以下版本不支持
- ⚠️ Android 15+ 需要测试验证

## 🔒 安全说明

### 隐私保护
- 应用不会上传个人数据
- 截图仅在本地处理
- 不会访问其他应用的敏感信息

### 使用限制
- 仅供学习和研究使用
- 遵守各平台使用条款
- 不要用于商业用途
- 注意使用频率和时间

## 📞 技术支持

### 获取帮助
- 查看应用内帮助文档
- 提交GitHub Issue
- 发送邮件至技术支持

### 贡献代码
- Fork项目仓库
- 创建功能分支
- 提交Pull Request
- 参与代码审查

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**免责声明**: 使用本软件的风险由用户自行承担。开发者不对任何直接或间接损失负责。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI配置工具
提供图形界面来配置自动化系统
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import asyncio
import threading
from config import Config
from bluetooth_controller import BluetoothController


class ConfigGUI:
    """配置GUI类"""
    
    def __init__(self):
        self.config = Config()
        self.bluetooth = None
        self.root = tk.Tk()
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("蓝牙自动化系统配置")
        self.root.geometry("800x600")
        
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 蓝牙配置页面
        self.create_bluetooth_tab(notebook)
        
        # 自动化配置页面
        self.create_automation_tab(notebook)
        
        # 检测配置页面
        self.create_detection_tab(notebook)
        
        # 安全配置页面
        self.create_safety_tab(notebook)
        
        # 平台配置页面
        self.create_platforms_tab(notebook)
        
        # 底部按钮
        self.create_bottom_buttons()
        
        # 加载当前配置
        self.load_current_config()
    
    def create_bluetooth_tab(self, notebook):
        """创建蓝牙配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="蓝牙设置")
        
        # 设备名称
        ttk.Label(frame, text="设备名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.device_name_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.device_name_var, width=30).grid(row=0, column=1, padx=5, pady=5)
        
        # 扫描超时
        ttk.Label(frame, text="扫描超时(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.scan_timeout_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.scan_timeout_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 连接超时
        ttk.Label(frame, text="连接超时(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.connection_timeout_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.connection_timeout_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 自动重连
        self.auto_reconnect_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="自动重连", variable=self.auto_reconnect_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 重连间隔
        ttk.Label(frame, text="重连间隔(秒):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.reconnect_interval_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.reconnect_interval_var, width=10).grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 蓝牙操作按钮
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="扫描设备", command=self.scan_bluetooth_devices).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="测试连接", command=self.test_bluetooth_connection).pack(side=tk.LEFT, padx=5)
        
        # 设备列表
        ttk.Label(frame, text="发现的设备:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.device_listbox = tk.Listbox(frame, height=6)
        self.device_listbox.grid(row=7, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        
        # 配置列权重
        frame.columnconfigure(1, weight=1)
    
    def create_automation_tab(self, notebook):
        """创建自动化配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="自动化设置")
        
        # 循环间隔
        ttk.Label(frame, text="循环间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.cycle_interval_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.cycle_interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 默认广告时长
        ttk.Label(frame, text="默认广告时长(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.default_ad_duration_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.default_ad_duration_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 最大广告时长
        ttk.Label(frame, text="最大广告时长(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_ad_duration_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.max_ad_duration_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 操作间隔
        ttk.Label(frame, text="操作间隔(秒):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.action_interval_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.action_interval_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 最大重试次数
        ttk.Label(frame, text="最大重试次数:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_retry_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.max_retry_var, width=10).grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 重试延迟
        ttk.Label(frame, text="重试延迟(秒):").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        self.retry_delay_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.retry_delay_var, width=10).grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)
    
    def create_detection_tab(self, notebook):
        """创建检测配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="检测设置")
        
        # 置信度阈值
        ttk.Label(frame, text="置信度阈值:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.confidence_threshold_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.confidence_threshold_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 模板匹配阈值
        ttk.Label(frame, text="模板匹配阈值:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.template_threshold_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.template_threshold_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 颜色容差
        ttk.Label(frame, text="颜色容差:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.color_tolerance_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.color_tolerance_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 调试输出
        self.debug_output_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="启用调试输出", variable=self.debug_output_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 保存检测图像
        self.save_detection_images_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="保存检测图像", variable=self.save_detection_images_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
    
    def create_safety_tab(self, notebook):
        """创建安全配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="安全设置")
        
        # 启用故障保护
        self.enable_failsafe_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="启用故障保护", variable=self.enable_failsafe_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 最大连续失败次数
        ttk.Label(frame, text="最大连续失败次数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_failures_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.max_failures_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 人类行为模拟
        self.human_behavior_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="人类行为模拟", variable=self.human_behavior_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 随机延迟
        self.random_delays_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="随机延迟", variable=self.random_delays_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 每日限制
        ttk.Label(frame, text="每日最大广告数:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.daily_max_ads_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.daily_max_ads_var, width=10).grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(frame, text="每日最大运行时间(小时):").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        self.daily_max_hours_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.daily_max_hours_var, width=10).grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)
    
    def create_platforms_tab(self, notebook):
        """创建平台配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="平台设置")
        
        # 平台列表
        ttk.Label(frame, text="支持的平台:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # 创建树形视图显示平台
        columns = ('name', 'package', 'reward', 'max_ads')
        self.platform_tree = ttk.Treeview(frame, columns=columns, show='headings', height=8)
        
        self.platform_tree.heading('name', text='平台名称')
        self.platform_tree.heading('package', text='包名')
        self.platform_tree.heading('reward', text='每广告奖励')
        self.platform_tree.heading('max_ads', text='最大广告数')
        
        self.platform_tree.grid(row=1, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        
        # 平台操作按钮
        platform_button_frame = ttk.Frame(frame)
        platform_button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(platform_button_frame, text="添加平台", command=self.add_platform).pack(side=tk.LEFT, padx=5)
        ttk.Button(platform_button_frame, text="编辑平台", command=self.edit_platform).pack(side=tk.LEFT, padx=5)
        ttk.Button(platform_button_frame, text="删除平台", command=self.delete_platform).pack(side=tk.LEFT, padx=5)
        
        frame.columnconfigure(1, weight=1)
    
    def create_bottom_buttons(self):
        """创建底部按钮"""
        button_frame = ttk.Frame(self.root)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置配置", command=self.reset_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导入配置", command=self.import_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出配置", command=self.export_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT, padx=5)
    
    def load_current_config(self):
        """加载当前配置到界面"""
        # 蓝牙配置
        bt_config = self.config.bluetooth_config
        self.device_name_var.set(bt_config.get('device_name', ''))
        self.scan_timeout_var.set(str(bt_config.get('scan_timeout', 10)))
        self.connection_timeout_var.set(str(bt_config.get('connection_timeout', 30)))
        self.auto_reconnect_var.set(bt_config.get('auto_reconnect', True))
        self.reconnect_interval_var.set(str(bt_config.get('reconnect_interval', 5)))
        
        # 自动化配置
        auto_config = self.config.automation_config
        self.cycle_interval_var.set(str(auto_config.get('cycle_interval', 2.0)))
        self.default_ad_duration_var.set(str(auto_config.get('default_ad_duration', 30)))
        self.max_ad_duration_var.set(str(auto_config.get('max_ad_duration', 60)))
        self.action_interval_var.set(str(auto_config.get('action_interval', 0.5)))
        self.max_retry_var.set(str(auto_config.get('max_retry_attempts', 3)))
        self.retry_delay_var.set(str(auto_config.get('retry_delay', 1.0)))
        
        # 检测配置
        det_config = self.config.detection_config
        self.confidence_threshold_var.set(str(det_config.get('confidence_threshold', 0.7)))
        self.template_threshold_var.set(str(det_config.get('template_match_threshold', 0.8)))
        self.color_tolerance_var.set(str(det_config.get('color_tolerance', 30)))
        self.debug_output_var.set(det_config.get('enable_debug_output', False))
        self.save_detection_images_var.set(det_config.get('save_detection_images', False))
        
        # 安全配置
        safety_config = self.config.safety_config
        self.enable_failsafe_var.set(safety_config.get('enable_failsafe', True))
        self.max_failures_var.set(str(safety_config.get('max_continuous_failures', 5)))
        self.human_behavior_var.set(safety_config.get('human_behavior_simulation', True))
        self.random_delays_var.set(safety_config.get('random_delays', True))
        
        # 每日限制
        daily_limits = self.config.get('tasks.daily_limits', {})
        self.daily_max_ads_var.set(str(daily_limits.get('max_ads', 100)))
        self.daily_max_hours_var.set(str(daily_limits.get('max_runtime_hours', 8)))
        
        # 加载平台配置
        self.load_platforms()
    
    def load_platforms(self):
        """加载平台配置到树形视图"""
        # 清空现有项目
        for item in self.platform_tree.get_children():
            self.platform_tree.delete(item)
        
        # 添加平台
        platforms = self.config.get('tasks.platforms', [])
        for platform in platforms:
            self.platform_tree.insert('', 'end', values=(
                platform.get('name', ''),
                platform.get('package_name', ''),
                platform.get('reward_per_ad', 0),
                platform.get('max_ads_per_session', 0)
            ))
    
    def save_config(self):
        """保存配置"""
        try:
            # 更新蓝牙配置
            self.config.set('bluetooth.device_name', self.device_name_var.get())
            self.config.set('bluetooth.scan_timeout', int(self.scan_timeout_var.get()))
            self.config.set('bluetooth.connection_timeout', int(self.connection_timeout_var.get()))
            self.config.set('bluetooth.auto_reconnect', self.auto_reconnect_var.get())
            self.config.set('bluetooth.reconnect_interval', int(self.reconnect_interval_var.get()))
            
            # 更新自动化配置
            self.config.set('automation.cycle_interval', float(self.cycle_interval_var.get()))
            self.config.set('automation.default_ad_duration', int(self.default_ad_duration_var.get()))
            self.config.set('automation.max_ad_duration', int(self.max_ad_duration_var.get()))
            self.config.set('automation.action_interval', float(self.action_interval_var.get()))
            self.config.set('automation.max_retry_attempts', int(self.max_retry_var.get()))
            self.config.set('automation.retry_delay', float(self.retry_delay_var.get()))
            
            # 更新检测配置
            self.config.set('detection.confidence_threshold', float(self.confidence_threshold_var.get()))
            self.config.set('detection.template_match_threshold', float(self.template_threshold_var.get()))
            self.config.set('detection.color_tolerance', int(self.color_tolerance_var.get()))
            self.config.set('detection.enable_debug_output', self.debug_output_var.get())
            self.config.set('detection.save_detection_images', self.save_detection_images_var.get())
            
            # 更新安全配置
            self.config.set('safety.enable_failsafe', self.enable_failsafe_var.get())
            self.config.set('safety.max_continuous_failures', int(self.max_failures_var.get()))
            self.config.set('safety.human_behavior_simulation', self.human_behavior_var.get())
            self.config.set('safety.random_delays', self.random_delays_var.get())
            
            # 更新每日限制
            self.config.set('tasks.daily_limits.max_ads', int(self.daily_max_ads_var.get()))
            self.config.set('tasks.daily_limits.max_runtime_hours', int(self.daily_max_hours_var.get()))
            
            # 保存配置文件
            if self.config.save_config():
                messagebox.showinfo("成功", "配置已保存")
            else:
                messagebox.showerror("错误", "保存配置失败")
                
        except ValueError as e:
            messagebox.showerror("错误", f"配置值无效: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置所有配置到默认值吗？"):
            self.config.reset_to_default()
            self.load_current_config()
            messagebox.showinfo("成功", "配置已重置")
    
    def import_config(self):
        """导入配置"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            if self.config.import_config(filename):
                self.load_current_config()
                messagebox.showinfo("成功", "配置已导入")
            else:
                messagebox.showerror("错误", "导入配置失败")
    
    def export_config(self):
        """导出配置"""
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            if self.config.export_config(filename):
                messagebox.showinfo("成功", "配置已导出")
            else:
                messagebox.showerror("错误", "导出配置失败")
    
    def scan_bluetooth_devices(self):
        """扫描蓝牙设备"""
        def scan_thread():
            try:
                # 创建蓝牙控制器
                bluetooth = BluetoothController(self.config.bluetooth_config)
                
                # 在新的事件循环中运行扫描
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                devices = loop.run_until_complete(bluetooth.scan_devices(timeout=10))
                
                # 更新设备列表
                self.root.after(0, self.update_device_list, devices)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"扫描设备失败: {e}"))
        
        # 在后台线程中运行扫描
        threading.Thread(target=scan_thread, daemon=True).start()
        messagebox.showinfo("提示", "正在扫描蓝牙设备，请稍候...")
    
    def update_device_list(self, devices):
        """更新设备列表"""
        self.device_listbox.delete(0, tk.END)
        for device in devices:
            self.device_listbox.insert(tk.END, f"{device['name']} ({device['address']})")
    
    def test_bluetooth_connection(self):
        """测试蓝牙连接"""
        selection = self.device_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个设备")
            return
        
        messagebox.showinfo("提示", "蓝牙连接测试功能待实现")
    
    def add_platform(self):
        """添加平台"""
        messagebox.showinfo("提示", "添加平台功能待实现")
    
    def edit_platform(self):
        """编辑平台"""
        messagebox.showinfo("提示", "编辑平台功能待实现")
    
    def delete_platform(self):
        """删除平台"""
        selection = self.platform_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个平台")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的平台吗？"):
            self.platform_tree.delete(selection[0])
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    app = ConfigGUI()
    app.run()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
提供简单的菜单界面来启动各种功能
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    蓝牙自动化脚本系统                          ║
    ║                  ESP32-C3 Bluetooth Automation               ║
    ║                                                              ║
    ║  功能：自动化看广告获得金币任务                                ║
    ║  硬件：ESP32-C3 + 蓝牙 + 屏幕截图                            ║
    ║  版本：v1.0.0                                                ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_dependencies():
    """检查依赖是否安装"""
    print("检查系统依赖...")
    
    required_modules = [
        'cv2', 'numpy', 'PIL', 'mss', 'pyautogui', 
        'bleak', 'asyncio', 'json', 'tkinter'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'cv2':
                import cv2
            elif module == 'PIL':
                from PIL import Image
            elif module == 'tkinter':
                import tkinter
            else:
                __import__(module)
            print(f"  ✓ {module}")
        except ImportError:
            print(f"  ✗ {module} (缺失)")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n缺少依赖模块: {', '.join(missing_modules)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("所有依赖检查通过！\n")
    return True


def check_files():
    """检查必要文件是否存在"""
    print("检查项目文件...")
    
    required_files = [
        'main.py',
        'bluetooth_controller.py',
        'screen_capture.py',
        'ad_detector.py',
        'task_executor.py',
        'config.py',
        'requirements.txt'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n缺少文件: {', '.join(missing_files)}")
        return False
    
    print("所有文件检查通过！\n")
    return True


def create_directories():
    """创建必要的目录"""
    directories = ['templates', 'logs', 'screenshots', 'configs']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")


def run_script(script_name, description):
    """运行指定脚本"""
    print(f"\n启动 {description}...")
    print("=" * 50)
    
    try:
        if script_name.endswith('.py'):
            subprocess.run([sys.executable, script_name], check=True)
        else:
            subprocess.run(script_name, shell=True, check=True)
    except subprocess.CalledProcessError as e:
        print(f"运行失败: {e}")
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
    
    input("\n按回车键返回主菜单...")


def show_system_info():
    """显示系统信息"""
    print("\n系统信息:")
    print("=" * 30)
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"操作系统: {os.name}")
    
    # 检查配置文件
    if os.path.exists('config.json'):
        print("配置文件: 存在")
    else:
        print("配置文件: 不存在（将使用默认配置）")
    
    # 检查模板目录
    template_dir = Path('templates')
    if template_dir.exists():
        template_count = len(list(template_dir.glob('*.png')))
        print(f"检测模板: {template_count}个")
    else:
        print("检测模板: 目录不存在")
    
    input("\n按回车键返回主菜单...")


def show_help():
    """显示帮助信息"""
    help_text = """
    使用帮助:
    ========
    
    1. 首次使用流程:
       - 运行"系统检查"确保环境正常
       - 使用"配置工具"进行基本设置
       - 运行"模板设置"创建检测模板
       - 执行"系统测试"验证功能
       - 启动"自动化脚本"开始工作
    
    2. ESP32-C3设置:
       - 烧录esp32_bluetooth_controller.ino固件
       - 连接LED到GPIO2，震动马达到GPIO4
       - 确保设备正常工作并可被发现
    
    3. 安全注意事项:
       - 首次运行建议使用测试模式
       - 设置合理的运行时间限制
       - 定期检查运行日志
       - 遇到问题及时停止
    
    4. 故障排除:
       - 蓝牙连接问题：检查设备和驱动
       - 检测不准确：更新模板和参数
       - 操作失败：检查权限和坐标
    
    更多信息请查看README.md文件。
    """
    print(help_text)
    input("\n按回车键返回主菜单...")


def main_menu():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("                    主菜单")
        print("=" * 60)
        print("1. 🚀 启动自动化脚本")
        print("2. ⚙️  配置工具")
        print("3. 🎯 模板设置工具")
        print("4. 🧪 系统测试")
        print("5. 📊 系统检查")
        print("6. ℹ️  系统信息")
        print("7. ❓ 帮助")
        print("0. 🚪 退出")
        print("=" * 60)
        
        choice = input("请选择功能 (0-7): ").strip()
        
        if choice == "1":
            run_script("main.py", "自动化脚本")
        
        elif choice == "2":
            run_script("gui_config.py", "配置工具")
        
        elif choice == "3":
            run_script("setup_templates.py", "模板设置工具")
        
        elif choice == "4":
            run_script("test_automation.py", "系统测试")
        
        elif choice == "5":
            print("\n系统检查:")
            print("=" * 30)
            deps_ok = check_dependencies()
            files_ok = check_files()
            
            if deps_ok and files_ok:
                print("✅ 系统检查通过，可以正常使用！")
            else:
                print("❌ 系统检查失败，请解决上述问题后重试。")
            
            input("\n按回车键返回主菜单...")
        
        elif choice == "6":
            show_system_info()
        
        elif choice == "7":
            show_help()
        
        elif choice == "0":
            print("\n感谢使用蓝牙自动化脚本系统！")
            print("如有问题请查看README.md或提交Issue。")
            break
        
        else:
            print("无效选择，请重试。")


def main():
    """主函数"""
    print_banner()
    
    # 创建必要目录
    create_directories()
    
    # 显示主菜单
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。")
    except Exception as e:
        print(f"\n程序发生错误: {e}")
        print("请检查错误信息并重试。")


if __name__ == "__main__":
    main()

plugins {
    id 'com.android.application'
}

android {
    namespace 'com.bluetooth.automation'
    compileSdk 34

    defaultConfig {
        applicationId "com.bluetooth.automation"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // 支持矢量图标
        vectorDrawables.useSupportLibrary = true
        
        // NDK配置（OpenCV需要）
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 签名配置
            signingConfig signingConfigs.release
        }
        
        debug {
            minifyEnabled false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }
    
    // 签名配置
    signingConfigs {
        release {
            // 在实际项目中，这些信息应该从gradle.properties或环境变量中读取
            storeFile file('keystore/release.keystore')
            storePassword 'your_store_password'
            keyAlias 'your_key_alias'
            keyPassword 'your_key_password'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildFeatures {
        viewBinding true
        dataBinding true
    }
    
    // 打包选项
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
}

dependencies {
    // Android核心库
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    
    // 生命周期组件
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'
    
    // 导航组件
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'
    
    // 权限处理
    implementation 'com.karumi:dexter:6.2.3'
    implementation 'pub.devrel:easypermissions:3.0.0'
    
    // 蓝牙库
    implementation 'com.github.douglasjunior:AndroidBluetoothLibrary:1.3.5'
    implementation 'no.nordicsemi.android:ble:2.6.1'
    
    // JSON处理
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'org.json:json:20231013'
    
    // 图像处理
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    
    // OpenCV (需要手动下载OpenCV Android SDK)
    implementation project(':opencv')
    
    // 网络请求（可选）
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    
    // 日志库
    implementation 'com.jakewharton.timber:timber:5.0.1'
    implementation 'com.orhanobut:logger:2.2.0'
    
    // 事件总线
    implementation 'org.greenrobot:eventbus:3.3.1'
    
    // 数据库（可选）
    implementation 'androidx.room:room-runtime:2.6.1'
    annotationProcessor 'androidx.room:room-compiler:2.6.1'
    
    // 工具库
    implementation 'com.blankj:utilcodex:1.31.1'
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.14'
    
    // UI组件
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.airbnb.android:lottie:6.2.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    
    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'androidx.test:core:1.5.0'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    
    // 调试工具
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
    debugImplementation 'com.facebook.flipper:flipper:0.212.0'
    debugImplementation 'com.facebook.soloader:soloader:0.10.5'
}

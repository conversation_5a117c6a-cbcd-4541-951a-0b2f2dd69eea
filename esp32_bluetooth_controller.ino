/*
 * ESP32-C3 蓝牙自动化控制器
 * 用于接收PC端命令并执行相应操作
 * 支持震动反馈、状态指示等功能
 */

#include "BluetoothSerial.h"
#include <ArduinoJson.h>
#include <WiFi.h>
#include <esp_bt.h>

// 蓝牙串口对象
BluetoothSerial SerialBT;

// 引脚定义
#define LED_PIN 2          // 状态指示LED
#define VIBRATOR_PIN 4     // 震动马达
#define BUTTON_PIN 0       // 用户按钮

// 状态变量
bool bluetoothConnected = false;
bool taskRunning = false;
unsigned long lastHeartbeat = 0;
unsigned long lastStatusSend = 0;
int batteryLevel = 100;

// 命令处理
String currentCommand = "";
bool commandReceived = false;

// 统计信息
struct Statistics {
  int commandsReceived = 0;
  int commandsExecuted = 0;
  int errorsCount = 0;
  unsigned long uptime = 0;
  unsigned long lastResetTime = 0;
};

Statistics stats;

void setup() {
  Serial.begin(115200);
  
  // 初始化引脚
  pinMode(LED_PIN, OUTPUT);
  pinMode(VIBRATOR_PIN, OUTPUT);
  pinMode(BUTTON_PIN, INPUT_PULLUP);
  
  // 初始化蓝牙
  initBluetooth();
  
  // 启动指示
  blinkLED(3, 200);
  
  Serial.println("ESP32-C3 蓝牙控制器已启动");
  stats.lastResetTime = millis();
}

void loop() {
  // 处理蓝牙连接
  handleBluetoothConnection();
  
  // 处理接收到的命令
  handleCommands();
  
  // 发送状态信息
  sendStatusUpdate();
  
  // 检查按钮
  handleButton();
  
  // 更新统计信息
  updateStatistics();
  
  delay(50); // 主循环延迟
}

void initBluetooth() {
  // 设置蓝牙设备名称
  SerialBT.begin("ESP32-C3-蓝牙控制器");
  
  // 设置蓝牙回调
  SerialBT.register_callback(bluetoothCallback);
  
  Serial.println("蓝牙已初始化，等待连接...");
  digitalWrite(LED_PIN, LOW);
}

void bluetoothCallback(esp_spp_cb_event_t event, esp_spp_cb_param_t *param) {
  switch (event) {
    case ESP_SPP_SRV_OPEN_EVT:
      Serial.println("蓝牙客户端已连接");
      bluetoothConnected = true;
      digitalWrite(LED_PIN, HIGH);
      sendWelcomeMessage();
      break;
      
    case ESP_SPP_CLOSE_EVT:
      Serial.println("蓝牙客户端已断开");
      bluetoothConnected = false;
      digitalWrite(LED_PIN, LOW);
      taskRunning = false;
      break;
      
    default:
      break;
  }
}

void handleBluetoothConnection() {
  // 检查连接状态
  if (bluetoothConnected) {
    lastHeartbeat = millis();
    
    // 检查是否有数据接收
    if (SerialBT.available()) {
      String receivedData = SerialBT.readString();
      receivedData.trim();
      
      if (receivedData.length() > 0) {
        currentCommand = receivedData;
        commandReceived = true;
        stats.commandsReceived++;
        
        Serial.println("收到命令: " + receivedData);
      }
    }
  } else {
    // 连接断开时的处理
    if (millis() - lastHeartbeat > 30000) { // 30秒无心跳
      Serial.println("连接超时，重启蓝牙");
      restartBluetooth();
    }
  }
}

void handleCommands() {
  if (!commandReceived) return;
  
  commandReceived = false;
  
  // 解析JSON命令
  DynamicJsonDocument doc(1024);
  DeserializationError error = deserializeJson(doc, currentCommand);
  
  if (error) {
    Serial.println("JSON解析失败: " + String(error.c_str()));
    sendErrorResponse("JSON_PARSE_ERROR", error.c_str());
    stats.errorsCount++;
    return;
  }
  
  String command = doc["command"];
  JsonObject data = doc["data"];
  
  // 执行命令
  bool success = executeCommand(command, data);
  
  if (success) {
    stats.commandsExecuted++;
    sendCommandResponse(command, "SUCCESS");
  } else {
    stats.errorsCount++;
    sendCommandResponse(command, "FAILED");
  }
}

bool executeCommand(String command, JsonObject data) {
  Serial.println("执行命令: " + command);
  
  if (command == "CONNECT_OK") {
    return handleConnectOk();
  }
  else if (command == "DISCONNECT") {
    return handleDisconnect();
  }
  else if (command == "START_WATCH_AD") {
    return handleStartWatchAd();
  }
  else if (command == "AD_COMPLETED") {
    return handleAdCompleted();
  }
  else if (command == "CLICK") {
    return handleClick(data);
  }
  else if (command == "SWIPE") {
    return handleSwipe(data);
  }
  else if (command == "VIBRATE") {
    return handleVibrate(data);
  }
  else if (command == "GET_STATUS") {
    return handleGetStatus();
  }
  else if (command == "SET_CONFIG") {
    return handleSetConfig(data);
  }
  else {
    Serial.println("未知命令: " + command);
    return false;
  }
}

bool handleConnectOk() {
  Serial.println("连接确认");
  blinkLED(2, 100);
  return true;
}

bool handleDisconnect() {
  Serial.println("断开连接");
  taskRunning = false;
  blinkLED(5, 50);
  return true;
}

bool handleStartWatchAd() {
  Serial.println("开始观看广告");
  taskRunning = true;
  
  // 震动提示
  vibrate(100);
  
  // LED快闪表示正在工作
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_PIN, LOW);
    delay(100);
    digitalWrite(LED_PIN, HIGH);
    delay(100);
  }
  
  return true;
}

bool handleAdCompleted() {
  Serial.println("广告观看完成");
  taskRunning = false;
  
  // 成功提示
  vibrate(200);
  delay(100);
  vibrate(200);
  
  return true;
}

bool handleClick(JsonObject data) {
  int x = data["x"];
  int y = data["y"];
  
  Serial.println("点击位置: (" + String(x) + ", " + String(y) + ")");
  
  // 震动反馈
  vibrate(50);
  
  return true;
}

bool handleSwipe(JsonObject data) {
  int startX = data["start_x"];
  int startY = data["start_y"];
  int endX = data["end_x"];
  int endY = data["end_y"];
  
  Serial.println("滑动: (" + String(startX) + ", " + String(startY) + 
                ") -> (" + String(endX) + ", " + String(endY) + ")");
  
  // 长震动反馈
  vibrate(150);
  
  return true;
}

bool handleVibrate(JsonObject data) {
  int duration = data["duration"] | 100; // 默认100ms
  
  Serial.println("震动: " + String(duration) + "ms");
  vibrate(duration);
  
  return true;
}

bool handleGetStatus() {
  sendStatusUpdate(true); // 强制发送状态
  return true;
}

bool handleSetConfig(JsonObject data) {
  // 这里可以设置一些配置参数
  Serial.println("设置配置");
  return true;
}

void sendWelcomeMessage() {
  DynamicJsonDocument doc(512);
  doc["type"] = "welcome";
  doc["device"] = "ESP32-C3";
  doc["version"] = "1.0.0";
  doc["timestamp"] = millis();
  
  String message;
  serializeJson(doc, message);
  SerialBT.println(message);
}

void sendCommandResponse(String command, String status) {
  DynamicJsonDocument doc(512);
  doc["type"] = "command_response";
  doc["command"] = command;
  doc["status"] = status;
  doc["timestamp"] = millis();
  
  String message;
  serializeJson(doc, message);
  SerialBT.println(message);
}

void sendErrorResponse(String errorType, String errorMessage) {
  DynamicJsonDocument doc(512);
  doc["type"] = "error";
  doc["error_type"] = errorType;
  doc["message"] = errorMessage;
  doc["timestamp"] = millis();
  
  String message;
  serializeJson(doc, message);
  SerialBT.println(message);
}

void sendStatusUpdate(bool force = false) {
  unsigned long currentTime = millis();
  
  // 每5秒发送一次状态，除非强制发送
  if (!force && currentTime - lastStatusSend < 5000) {
    return;
  }
  
  lastStatusSend = currentTime;
  
  DynamicJsonDocument doc(1024);
  doc["type"] = "status";
  doc["timestamp"] = currentTime;
  doc["connected"] = bluetoothConnected;
  doc["task_running"] = taskRunning;
  doc["battery_level"] = getBatteryLevel();
  doc["uptime"] = currentTime - stats.lastResetTime;
  
  // 统计信息
  JsonObject statsObj = doc.createNestedObject("statistics");
  statsObj["commands_received"] = stats.commandsReceived;
  statsObj["commands_executed"] = stats.commandsExecuted;
  statsObj["errors_count"] = stats.errorsCount;
  statsObj["success_rate"] = stats.commandsReceived > 0 ? 
    (float)stats.commandsExecuted / stats.commandsReceived * 100 : 0;
  
  String message;
  serializeJson(doc, message);
  SerialBT.println(message);
}

void handleButton() {
  static unsigned long lastButtonPress = 0;
  static bool lastButtonState = HIGH;
  
  bool currentButtonState = digitalRead(BUTTON_PIN);
  
  if (currentButtonState == LOW && lastButtonState == HIGH) {
    unsigned long currentTime = millis();
    
    // 防抖动
    if (currentTime - lastButtonPress > 200) {
      lastButtonPress = currentTime;
      
      Serial.println("按钮按下");
      
      // 按钮功能：切换任务状态
      taskRunning = !taskRunning;
      
      if (taskRunning) {
        Serial.println("手动启动任务");
        blinkLED(2, 200);
      } else {
        Serial.println("手动停止任务");
        blinkLED(5, 100);
      }
      
      // 发送按钮事件
      DynamicJsonDocument doc(256);
      doc["type"] = "button_event";
      doc["action"] = taskRunning ? "start" : "stop";
      doc["timestamp"] = currentTime;
      
      String message;
      serializeJson(doc, message);
      SerialBT.println(message);
    }
  }
  
  lastButtonState = currentButtonState;
}

void updateStatistics() {
  stats.uptime = millis() - stats.lastResetTime;
}

int getBatteryLevel() {
  // 这里可以读取实际的电池电压
  // 简化实现：模拟电池电量
  static unsigned long lastBatteryUpdate = 0;
  static int simulatedBattery = 100;
  
  if (millis() - lastBatteryUpdate > 60000) { // 每分钟更新一次
    lastBatteryUpdate = millis();
    simulatedBattery = max(0, simulatedBattery - 1); // 模拟电量消耗
  }
  
  return simulatedBattery;
}

void vibrate(int duration) {
  digitalWrite(VIBRATOR_PIN, HIGH);
  delay(duration);
  digitalWrite(VIBRATOR_PIN, LOW);
}

void blinkLED(int times, int interval) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(interval);
    digitalWrite(LED_PIN, LOW);
    delay(interval);
  }
}

void restartBluetooth() {
  Serial.println("重启蓝牙...");
  SerialBT.end();
  delay(1000);
  initBluetooth();
}

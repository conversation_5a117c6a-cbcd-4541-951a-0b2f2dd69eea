package com.bluetooth.automation;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Set;
import java.util.UUID;

/**
 * 蓝牙自动化控制主界面
 * 连接ESP32-C3设备，实现自动化控制功能
 */
public class MainActivity extends AppCompatActivity {
    
    private static final String TAG = "BluetoothAutomation";
    private static final int REQUEST_ENABLE_BT = 1;
    private static final int REQUEST_PERMISSIONS = 2;
    
    // ESP32设备信息
    private static final String ESP32_NAME = "ESP32-C3-蓝牙控制器";
    private static final UUID ESP32_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    
    // 蓝牙相关
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothSocket bluetoothSocket;
    private BluetoothDevice esp32Device;
    private OutputStream outputStream;
    private InputStream inputStream;
    private boolean isConnected = false;
    
    // UI组件
    private Button btnConnect;
    private Button btnStartTask;
    private Button btnStopTask;
    private Button btnScreenshot;
    private TextView tvStatus;
    private TextView tvStats;
    
    // 自动化控制
    private AutomationController automationController;
    private ScreenCaptureService screenCaptureService;
    private AdDetectionService adDetectionService;
    
    // 统计信息
    private int adsWatched = 0;
    private int coinsEarned = 0;
    private boolean taskRunning = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        initBluetooth();
        initServices();
        checkPermissions();
    }
    
    private void initViews() {
        btnConnect = findViewById(R.id.btn_connect);
        btnStartTask = findViewById(R.id.btn_start_task);
        btnStopTask = findViewById(R.id.btn_stop_task);
        btnScreenshot = findViewById(R.id.btn_screenshot);
        tvStatus = findViewById(R.id.tv_status);
        tvStats = findViewById(R.id.tv_stats);
        
        // 设置按钮点击事件
        btnConnect.setOnClickListener(v -> toggleBluetoothConnection());
        btnStartTask.setOnClickListener(v -> startAutomationTask());
        btnStopTask.setOnClickListener(v -> stopAutomationTask());
        btnScreenshot.setOnClickListener(v -> takeScreenshot());
        
        // 初始状态
        updateUI();
    }
    
    private void initBluetooth() {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        
        if (bluetoothAdapter == null) {
            Toast.makeText(this, "设备不支持蓝牙", Toast.LENGTH_LONG).show();
            finish();
            return;
        }
        
        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
        }
    }
    
    private void initServices() {
        automationController = new AutomationController(this);
        screenCaptureService = new ScreenCaptureService(this);
        adDetectionService = new AdDetectionService(this);
    }
    
    private void checkPermissions() {
        String[] permissions = {
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.SYSTEM_ALERT_WINDOW
        };
        
        boolean allGranted = true;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allGranted = false;
                break;
            }
        }
        
        if (!allGranted) {
            ActivityCompat.requestPermissions(this, permissions, REQUEST_PERMISSIONS);
        }
    }
    
    private void toggleBluetoothConnection() {
        if (isConnected) {
            disconnectBluetooth();
        } else {
            connectBluetooth();
        }
    }
    
    private void connectBluetooth() {
        // 查找ESP32设备
        Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
        
        for (BluetoothDevice device : pairedDevices) {
            if (ESP32_NAME.equals(device.getName())) {
                esp32Device = device;
                break;
            }
        }
        
        if (esp32Device == null) {
            Toast.makeText(this, "未找到ESP32设备，请先配对", Toast.LENGTH_LONG).show();
            return;
        }
        
        // 在后台线程中连接
        new Thread(() -> {
            try {
                bluetoothSocket = esp32Device.createRfcommSocketToServiceRecord(ESP32_UUID);
                bluetoothSocket.connect();
                
                outputStream = bluetoothSocket.getOutputStream();
                inputStream = bluetoothSocket.getInputStream();
                
                isConnected = true;
                
                runOnUiThread(() -> {
                    updateUI();
                    Toast.makeText(this, "蓝牙连接成功", Toast.LENGTH_SHORT).show();
                    sendCommand("CONNECT_OK", null);
                });
                
                // 开始监听数据
                startListening();
                
            } catch (IOException e) {
                Log.e(TAG, "蓝牙连接失败", e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "蓝牙连接失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }
    
    private void disconnectBluetooth() {
        try {
            if (bluetoothSocket != null) {
                sendCommand("DISCONNECT", null);
                bluetoothSocket.close();
            }
            isConnected = false;
            updateUI();
            Toast.makeText(this, "蓝牙已断开", Toast.LENGTH_SHORT).show();
        } catch (IOException e) {
            Log.e(TAG, "断开蓝牙失败", e);
        }
    }
    
    private void startListening() {
        new Thread(() -> {
            byte[] buffer = new byte[1024];
            int bytes;
            
            while (isConnected) {
                try {
                    bytes = inputStream.read(buffer);
                    String receivedData = new String(buffer, 0, bytes);
                    
                    runOnUiThread(() -> handleReceivedData(receivedData));
                    
                } catch (IOException e) {
                    Log.e(TAG, "读取数据失败", e);
                    break;
                }
            }
        }).start();
    }
    
    private void handleReceivedData(String data) {
        try {
            JSONObject json = new JSONObject(data);
            String type = json.getString("type");
            
            switch (type) {
                case "status":
                    handleStatusUpdate(json);
                    break;
                case "button_event":
                    handleButtonEvent(json);
                    break;
                case "error":
                    handleError(json);
                    break;
            }
        } catch (JSONException e) {
            Log.e(TAG, "解析JSON失败", e);
        }
    }
    
    private void handleStatusUpdate(JSONObject status) {
        try {
            boolean taskRunning = status.getBoolean("task_running");
            int batteryLevel = status.getInt("battery_level");
            
            String statusText = String.format("设备状态: %s\n电量: %d%%", 
                taskRunning ? "运行中" : "待机", batteryLevel);
            
            tvStatus.setText(statusText);
            
        } catch (JSONException e) {
            Log.e(TAG, "处理状态更新失败", e);
        }
    }
    
    private void handleButtonEvent(JSONObject event) {
        try {
            String action = event.getString("action");
            
            if ("start".equals(action)) {
                startAutomationTask();
            } else if ("stop".equals(action)) {
                stopAutomationTask();
            }
            
        } catch (JSONException e) {
            Log.e(TAG, "处理按钮事件失败", e);
        }
    }
    
    private void handleError(JSONObject error) {
        try {
            String message = error.getString("message");
            Toast.makeText(this, "设备错误: " + message, Toast.LENGTH_LONG).show();
        } catch (JSONException e) {
            Log.e(TAG, "处理错误信息失败", e);
        }
    }
    
    private void sendCommand(String command, JSONObject data) {
        if (!isConnected || outputStream == null) {
            return;
        }
        
        new Thread(() -> {
            try {
                JSONObject json = new JSONObject();
                json.put("command", command);
                json.put("timestamp", System.currentTimeMillis());
                
                if (data != null) {
                    json.put("data", data);
                }
                
                String message = json.toString() + "\n";
                outputStream.write(message.getBytes());
                outputStream.flush();
                
                Log.d(TAG, "发送命令: " + command);
                
            } catch (IOException | JSONException e) {
                Log.e(TAG, "发送命令失败", e);
            }
        }).start();
    }
    
    private void startAutomationTask() {
        if (!isConnected) {
            Toast.makeText(this, "请先连接蓝牙设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        taskRunning = true;
        updateUI();
        
        // 发送开始命令到ESP32
        sendCommand("START_WATCH_AD", null);
        
        // 开始自动化任务
        automationController.startAutomation();
        
        Toast.makeText(this, "自动化任务已启动", Toast.LENGTH_SHORT).show();
    }
    
    private void stopAutomationTask() {
        taskRunning = false;
        updateUI();
        
        // 发送停止命令到ESP32
        sendCommand("AD_COMPLETED", null);
        
        // 停止自动化任务
        automationController.stopAutomation();
        
        Toast.makeText(this, "自动化任务已停止", Toast.LENGTH_SHORT).show();
    }
    
    private void takeScreenshot() {
        screenCaptureService.captureScreen(new ScreenCaptureService.ScreenshotCallback() {
            @Override
            public void onScreenshotTaken(Bitmap screenshot) {
                // 检测广告
                adDetectionService.detectAd(screenshot, new AdDetectionService.DetectionCallback() {
                    @Override
                    public void onAdDetected(AdDetectionService.AdInfo adInfo) {
                        if (adInfo.hasAd) {
                            handleAdDetected(adInfo);
                        } else {
                            Toast.makeText(MainActivity.this, "未检测到广告", Toast.LENGTH_SHORT).show();
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                Toast.makeText(MainActivity.this, "截图失败: " + error, Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private void handleAdDetected(AdDetectionService.AdInfo adInfo) {
        String message = String.format("检测到%s广告，置信度: %.2f", 
            adInfo.type, adInfo.confidence);
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        
        // 发送点击命令到ESP32
        if (adInfo.closeButtonPosition != null) {
            try {
                JSONObject data = new JSONObject();
                data.put("x", adInfo.closeButtonPosition.x);
                data.put("y", adInfo.closeButtonPosition.y);
                sendCommand("CLICK", data);
            } catch (JSONException e) {
                Log.e(TAG, "发送点击命令失败", e);
            }
        }
        
        // 更新统计
        adsWatched++;
        coinsEarned += adInfo.reward;
        updateStats();
    }
    
    private void updateUI() {
        btnConnect.setText(isConnected ? "断开连接" : "连接设备");
        btnStartTask.setEnabled(isConnected && !taskRunning);
        btnStopTask.setEnabled(isConnected && taskRunning);
        btnScreenshot.setEnabled(isConnected);
        
        if (!isConnected) {
            tvStatus.setText("设备未连接");
        }
    }
    
    private void updateStats() {
        String stats = String.format("已观看广告: %d\n获得金币: %d", adsWatched, coinsEarned);
        tvStats.setText(stats);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (isConnected) {
            disconnectBluetooth();
        }
        
        if (automationController != null) {
            automationController.cleanup();
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode != RESULT_OK) {
                Toast.makeText(this, "需要启用蓝牙才能使用", Toast.LENGTH_LONG).show();
                finish();
            }
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_PERMISSIONS) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (!allGranted) {
                Toast.makeText(this, "需要所有权限才能正常使用", Toast.LENGTH_LONG).show();
            }
        }
    }
}

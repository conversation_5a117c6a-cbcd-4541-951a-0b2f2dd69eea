{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Insert Image as Attachment": "將影像插入為附件"}, "package": {"cleanInvalidImageAttachment.title": "清除不正確影像附件參考", "copyCellOutput.title": "複製儲存格輸出", "description": "提供開啟和讀取 Jupyter .ipynb 筆記本檔案的基本支援", "displayName": ".ipynb 支援", "ipynb.experimental.serialization": "Experimental feature to serialize the <PERSON><PERSON><PERSON> notebook in a worker thread.", "ipynb.pasteImagesAsAttachments.enabled": "啟用/停用將影像貼到 IPYNB Notebook 檔案中的 Markdown 儲存格。貼上的影像會插入為儲存格的附件。", "markdownAttachmentRenderer.displayName": "Markdown-It ipynb 資料格附件轉譯器", "newUntitledIpynb.shortTitle": "Jupyter Notebook", "newUntitledIpynb.title": "新 Jupyter Notebook", "openCellOutput.title": "在文字編輯器中開啟儲存格輸出", "openIpynbInNotebookEditor.title": "在筆記本編輯器中開啟 IPYNB 檔案"}}}